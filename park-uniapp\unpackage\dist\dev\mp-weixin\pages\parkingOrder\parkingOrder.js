"use strict";
const common_vendor = require("../../common/vendor.js");
const api_parkingOrder = require("../../api/parkingOrder.js");
const api_invoice = require("../../api/invoice.js");
if (!Array) {
  const _easycom_up_icon2 = common_vendor.resolveComponent("up-icon");
  const _easycom_up_empty2 = common_vendor.resolveComponent("up-empty");
  const _easycom_up_input2 = common_vendor.resolveComponent("up-input");
  const _easycom_up_popup2 = common_vendor.resolveComponent("up-popup");
  (_easycom_up_icon2 + _easycom_up_empty2 + _easycom_up_input2 + _easycom_up_popup2)();
}
const _easycom_up_icon = () => "../../node-modules/uview-plus/components/u-icon/u-icon.js";
const _easycom_up_empty = () => "../../node-modules/uview-plus/components/u-empty/u-empty.js";
const _easycom_up_input = () => "../../node-modules/uview-plus/components/u-input/u-input.js";
const _easycom_up_popup = () => "../../node-modules/uview-plus/components/u-popup/u-popup.js";
if (!Math) {
  (_easycom_up_icon + _easycom_up_empty + _easycom_up_input + _easycom_up_popup)();
}
const _sfc_main = {
  __name: "parkingOrder",
  setup(__props) {
    const orderList = common_vendor.ref([]);
    const activeFilter = common_vendor.ref(0);
    const showPopup = common_vendor.ref(false);
    const invoiceId = common_vendor.ref(null);
    const notifyEmail = common_vendor.ref("");
    const placeholderStyle = {
      color: "#616161"
    };
    const emailInputStyle = {
      paddingLeft: "20rpx",
      paddingTop: "26rpx",
      paddingBottom: "26rpx",
      borderBottom: "1rpx solid rgba(189,189,189,0.2)"
    };
    common_vendor.onShow(() => {
      getOrderList(0);
    });
    const setFilter = async (payStatus) => {
      if (activeFilter.value === payStatus)
        return;
      activeFilter.value = payStatus;
      await getOrderList(payStatus);
    };
    const getOrderList = async (payStatus) => {
      try {
        common_vendor.index.showLoading({
          title: "加载中...",
          mask: true
        });
        const params = {};
        if (payStatus !== null) {
          params.payStatus = payStatus;
        }
        const res = await api_parkingOrder.getParkingOrderList(params);
        console.log("停车订单数据:", res);
        orderList.value = res.data || [];
      } catch (error) {
        console.error("获取停车订单失败:", error);
        common_vendor.index.showToast({
          title: "获取订单失败",
          icon: "none"
        });
      } finally {
        common_vendor.index.hideLoading();
      }
    };
    const formatDuration = (minutes) => {
      if (!minutes)
        return "0分钟";
      const hours = Math.floor(minutes / 60);
      const mins = minutes % 60;
      if (hours > 0) {
        return mins > 0 ? `${hours}小时${mins}分钟` : `${hours}小时`;
      } else {
        return `${mins}分钟`;
      }
    };
    const getStatusText = (status) => {
      switch (status) {
        case 1:
          return "进行中";
        case 4:
          return "已退款";
        case 5:
          return "已支付";
        default:
          return "未知状态";
      }
    };
    const getStatusClass = (status) => {
      switch (status) {
        case 1:
          return "status-progress";
        case 4:
          return "status-failed";
        case 5:
          return "status-paid";
        default:
          return "status-unknown";
      }
    };
    const goToPay = (item) => {
      common_vendor.index.navigateTo({
        url: `/pages/payPlateDetail/payPlateDetail?plateNo=${item.plateNo}&warehouseId=${item.warehouseId}`
      });
    };
    const routeToInvoice = (item) => {
      if (item.miniInvoiceRecord && item.miniInvoiceRecord.id) {
        if (item.miniInvoiceRecord.isResume) {
          common_vendor.index.navigateTo({
            url: `/pages/invoice/openInvoice?functionId=${item.id}&money=${item.actualPayment}&functionType=1&invoiceId=${item.miniInvoiceRecord.id}&isResume=${item.miniInvoiceRecord.isResume}`
          });
        } else {
          common_vendor.index.navigateTo({
            url: `/pages/invoice/openInvoice?functionId=${item.id}&money=${item.actualPayment}&functionType=1&invoiceId=${item.miniInvoiceRecord.id}`
          });
        }
      } else {
        common_vendor.index.navigateTo({
          url: `/pages/invoice/openInvoice?functionId=${item.id}&money=${item.actualPayment}&functionType=1`
        });
      }
    };
    const chooseEmail = (id) => {
      invoiceId.value = id;
      showPopup.value = true;
    };
    const handlePostInvoiceSend = async () => {
      if (!notifyEmail.value) {
        common_vendor.index.showToast({
          title: "请输入邮箱",
          icon: "none"
        });
        return;
      }
      try {
        const params = {
          id: invoiceId.value,
          notifyEmail: notifyEmail.value
        };
        await api_invoice.postInvoiceSend(params);
        common_vendor.index.showToast({
          title: "邮箱发送成功～",
          icon: "none",
          duration: 2e3
        });
        setTimeout(() => {
          showPopup.value = false;
          notifyEmail.value = "";
        }, 1e3);
      } catch (error) {
        console.error("发送邮箱失败:", error);
        common_vendor.index.showToast({
          title: "发送失败，请重试",
          icon: "none"
        });
      }
    };
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: activeFilter.value === 0 ? 1 : "",
        b: common_vendor.o(($event) => setFilter(0)),
        c: activeFilter.value === 1 ? 1 : "",
        d: common_vendor.o(($event) => setFilter(1)),
        e: activeFilter.value === 5 ? 1 : "",
        f: common_vendor.o(($event) => setFilter(5)),
        g: activeFilter.value === 4 ? 1 : "",
        h: common_vendor.o(($event) => setFilter(4)),
        i: orderList.value.length > 0
      }, orderList.value.length > 0 ? {
        j: common_vendor.f(orderList.value, (item, k0, i0) => {
          return common_vendor.e({
            a: common_vendor.t(item.tradeId || "订单号待生成"),
            b: common_vendor.t(getStatusText(item.payStatus)),
            c: common_vendor.n(getStatusClass(item.payStatus)),
            d: common_vendor.t(item.warehouseName || "未知停车场"),
            e: common_vendor.t(item.plateNo),
            f: common_vendor.t(item.beginParkingTime),
            g: common_vendor.t(item.endParkingTime),
            h: common_vendor.t(formatDuration(item.parkingDuration)),
            i: common_vendor.t(item.paymentTime),
            j: item.discountAmount > 0
          }, item.discountAmount > 0 ? {
            k: common_vendor.t(item.discountAmount)
          } : {}, {
            l: common_vendor.t(item.actualPayment),
            m: item.payStatus === 1
          }, item.payStatus === 1 ? {
            n: "f5c8e10e-0-" + i0,
            o: common_vendor.p({
              name: "rmb-circle-fill",
              color: "#ffffff",
              size: "14"
            }),
            p: common_vendor.o(($event) => goToPay(item), item.id)
          } : {}, {
            q: item.payStatus === 5
          }, item.payStatus === 5 ? common_vendor.e({
            r: item.miniInvoiceRecord && item.miniInvoiceRecord.status === "ISSUED"
          }, item.miniInvoiceRecord && item.miniInvoiceRecord.status === "ISSUED" ? {
            s: "f5c8e10e-1-" + i0,
            t: common_vendor.p({
              name: "email",
              color: "#3b82f6",
              size: "14"
            }),
            v: common_vendor.o(($event) => chooseEmail(item.miniInvoiceRecord.id), item.id)
          } : {}, {
            w: !item.miniInvoiceRecord || item.miniInvoiceRecord && (item.miniInvoiceRecord.status === "UNISSU" || item.miniInvoiceRecord.status === "CLOSED") && !item.miniInvoiceRecord.reopenSign
          }, !item.miniInvoiceRecord || item.miniInvoiceRecord && (item.miniInvoiceRecord.status === "UNISSU" || item.miniInvoiceRecord.status === "CLOSED") && !item.miniInvoiceRecord.reopenSign ? {
            x: "f5c8e10e-2-" + i0,
            y: common_vendor.p({
              name: "file-text",
              color: "#ffffff",
              size: "14"
            }),
            z: common_vendor.o(($event) => routeToInvoice(item), item.id)
          } : {}, {
            A: item.miniInvoiceRecord && (item.miniInvoiceRecord.status === "ISSUED" && !item.miniInvoiceRecord.reopenSign || item.miniInvoiceRecord.status === "CLOSED" && item.miniInvoiceRecord.reopenSign || item.miniInvoiceRecord.status === "REVERSED")
          }, item.miniInvoiceRecord && (item.miniInvoiceRecord.status === "ISSUED" && !item.miniInvoiceRecord.reopenSign || item.miniInvoiceRecord.status === "CLOSED" && item.miniInvoiceRecord.reopenSign || item.miniInvoiceRecord.status === "REVERSED") ? {
            B: "f5c8e10e-3-" + i0,
            C: common_vendor.p({
              name: "reload",
              color: "#ffffff",
              size: "14"
            }),
            D: common_vendor.o(($event) => routeToInvoice(item), item.id)
          } : {}) : {}, {
            E: item.id
          });
        })
      } : {
        k: common_vendor.p({
          text: "暂无相关记录",
          color: "#64748b"
        })
      }, {
        l: common_vendor.o(($event) => notifyEmail.value = $event),
        m: common_vendor.p({
          border: "none",
          placeholder: "请输入您的电子邮箱",
          clearable: true,
          fontSize: "28rpx",
          color: "#616161",
          placeholderStyle,
          customStyle: emailInputStyle,
          modelValue: notifyEmail.value
        }),
        n: common_vendor.o(($event) => showPopup.value = false),
        o: common_vendor.o(handlePostInvoiceSend),
        p: common_vendor.o(($event) => showPopup.value = false),
        q: common_vendor.p({
          show: showPopup.value,
          mode: "center",
          round: 10,
          safeAreaInsetBottom: false,
          closeOnClickOverlay: true
        })
      });
    };
  }
};
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-f5c8e10e"]]);
wx.createPage(MiniProgramPage);
