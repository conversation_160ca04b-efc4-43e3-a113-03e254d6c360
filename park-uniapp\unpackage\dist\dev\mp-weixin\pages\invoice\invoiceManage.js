"use strict";
const common_vendor = require("../../common/vendor.js");
const api_invoice = require("../../api/invoice.js");
if (!Array) {
  const _easycom_up_icon2 = common_vendor.resolveComponent("up-icon");
  const _easycom_up_empty2 = common_vendor.resolveComponent("up-empty");
  (_easycom_up_icon2 + _easycom_up_empty2)();
}
const _easycom_up_icon = () => "../../node-modules/uview-plus/components/u-icon/u-icon.js";
const _easycom_up_empty = () => "../../node-modules/uview-plus/components/u-empty/u-empty.js";
if (!Math) {
  (_easycom_up_icon + _easycom_up_empty)();
}
const _sfc_main = {
  __name: "invoiceManage",
  setup(__props) {
    const invoiceList = common_vendor.ref([]);
    const loading = common_vendor.ref(false);
    common_vendor.onMounted(() => {
      fetchInvoiceList();
    });
    const fetchInvoiceList = async () => {
      if (loading.value)
        return;
      try {
        loading.value = true;
        common_vendor.index.showLoading({
          title: "加载中...",
          mask: true
        });
        const res = await api_invoice.getInvoiceRecordList();
        console.log("发票记录数据:", res);
        invoiceList.value = res.data || res.rows || [];
      } catch (error) {
        console.error("获取发票列表失败:", error);
        common_vendor.index.showToast({
          title: "获取发票列表失败",
          icon: "none"
        });
      } finally {
        loading.value = false;
        common_vendor.index.hideLoading();
      }
    };
    const getStatusText = (status) => {
      switch (status) {
        case "PENDING":
          return "待开具";
        case "ISSUING":
          return "开具中";
        case "ISSUED":
          return "已开具";
        case "REVERSING":
          return "红冲中";
        case "REVERSED":
          return "已红冲";
        case "CLOSED":
          return "已关闭";
        default:
          return "-";
      }
    };
    const getStatusColor = (status) => {
      switch (status) {
        case "PENDING":
          return "#3b82f6";
        case "ISSUING":
          return "#f59e0b";
        case "ISSUED":
          return "#0ea5e9";
        case "REVERSING":
          return "#ef4444";
        case "REVERSED":
          return "#6b7280";
        case "CLOSED":
          return "#9ca3af";
        default:
          return "#6b7280";
      }
    };
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: invoiceList.value.length > 0
      }, invoiceList.value.length > 0 ? {
        b: common_vendor.f(invoiceList.value, (item, index, i0) => {
          return common_vendor.e({
            a: item.functionType === 1
          }, item.functionType === 1 ? {
            b: common_vendor.t(item.tradeId)
          } : item.functionType === 2 ? {
            d: common_vendor.t(item.tradeId)
          } : {}, {
            c: item.functionType === 2,
            e: common_vendor.t(getStatusText(item.status)),
            f: getStatusColor(item.status),
            g: common_vendor.t(item.invoiceTitleContent || "个人"),
            h: common_vendor.t(item.invoiceType === 0 ? "普通发票" : "专用发票"),
            i: common_vendor.t(item.notifyEmail || "-"),
            j: item.status === "CLOSED"
          }, item.status === "CLOSED" ? {
            k: common_vendor.t(item.remark || "-")
          } : {}, {
            l: "4863d9d0-0-" + i0,
            m: common_vendor.t(item.issueDate || "-"),
            n: common_vendor.t(item.totalMoney || "0"),
            o: item.id,
            p: common_vendor.n(index === invoiceList.value.length - 1 ? "last-item" : "")
          });
        }),
        c: common_vendor.p({
          name: "clock",
          color: "#666666",
          size: "14"
        })
      } : {
        d: common_vendor.p({
          text: "暂无发票记录",
          color: "#64748b"
        })
      });
    };
  }
};
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-4863d9d0"]]);
wx.createPage(MiniProgramPage);
