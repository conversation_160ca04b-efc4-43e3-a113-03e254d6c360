<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lgjy.system.mapper.DashboardMapper">

    <!-- 获取总场库数量 -->
    <select id="getTotalWarehouses" resultType="java.lang.Long">
        <choose>
            <when test="warehouseId != null">
                <!-- 如果指定了场库ID，返回1（表示当前选中的场库） -->
                SELECT 1
            </when>
            <otherwise>
                <!-- 如果没有指定场库ID，返回总场库数量 -->
                SELECT COUNT(*)
                FROM mini_warehouse
                WHERE delete_flag = 0
            </otherwise>
        </choose>
    </select>

    <!-- 获取总车位数量 -->
    <select id="getTotalParkingSpaces" resultType="java.lang.Long">
        SELECT COALESCE(SUM(total_parking), 0)
        FROM mini_warehouse
        WHERE delete_flag = 0
          AND total_parking IS NOT NULL
        <if test="warehouseId != null">
            AND id = #{warehouseId}
        </if>
    </select>

    <!-- 获取会员总数量 -->
    <select id="getTotalMembers" resultType="java.lang.Long">
        <choose>
            <when test="warehouseId != null">
                <!-- 获取特定场库的会员数量，通过mini_special_user_car表关联 -->
                SELECT COUNT(DISTINCT msu.id)
                FROM mini_special_user msu
                INNER JOIN mini_special_user_car msuc ON msu.id = msuc.special_user_id
                WHERE msu.delete_flag = 0
                  AND msuc.delete_flag = 0
                  AND msuc.warehouse_id = #{warehouseId}
            </when>
            <otherwise>
                <!-- 获取全系统会员数量 -->
                SELECT COUNT(*)
                FROM mini_special_user
                WHERE delete_flag = 0
            </otherwise>
        </choose>
    </select>

    <!-- 获取小程序用户总数 -->
    <select id="getTotalAppUsers" resultType="java.lang.Long">
        SELECT COUNT(*)
        FROM wx_user
        WHERE delete_flag = 0
        <if test="warehouseId != null">
            <!-- TODO: 根据实际业务需求添加场库关联条件，如通过停车记录关联 -->
            AND EXISTS (
                SELECT 1 FROM mini_parking_order mpo
                WHERE mpo.user_id = wx_user.id
                AND mpo.warehouse_id = #{warehouseId}
                AND mpo.delete_flag = 0
            )
        </if>
    </select>

    <!-- 获取特殊会员数量（VIP客户和集团客户） -->
    <select id="getSpecialMemberCount" resultType="java.lang.Long">
        SELECT COUNT(*)
        FROM mini_vip_package_user_detail
        WHERE delete_flag = 0
          AND vip_type IN (1, 2)  <!-- 1=集团客户, 2=VIP客户 -->
    </select>

    <!-- 获取当日进场车次 -->
    <select id="getTodayEntryCount" resultType="java.lang.Long">
        SELECT COUNT(*)
        FROM gate_parking_info
        WHERE DATE(FROM_UNIXTIME(CAST(inTime AS UNSIGNED))) = CURDATE()
          AND inTime IS NOT NULL
          AND inTime != ''
          AND inTime != '0'
    </select>

    <!-- 获取当日出场车次 -->
    <select id="getTodayExitCount" resultType="java.lang.Long">
        SELECT COUNT(*)
        FROM gate_parking_info
        WHERE DATE(FROM_UNIXTIME(CAST(outTime AS UNSIGNED))) = CURDATE()
          AND outTime IS NOT NULL
          AND outTime != ''
          AND outTime != '0'
    </select>

    <!-- 获取所有场库的经纬度信息 -->
    <select id="getWarehouseLocations" resultType="java.util.Map">
        SELECT
            id,
            parking_no as parkingNo,
            warehouse_name as warehouseName,
            longitude,
            latitude,
            address
        FROM mini_warehouse
        WHERE delete_flag = 0
          AND longitude IS NOT NULL
          AND latitude IS NOT NULL
          AND longitude != ''
          AND latitude != ''
    </select>

    <!-- 获取收入金额和总订单量前五名的场库 -->
    <select id="getTopWarehousesByRevenue" resultType="java.util.Map">
        SELECT
            mw.id as warehouseId,
            mw.warehouse_name as warehouseName,
            COALESCE(SUM(mpo.actual_payment), 0) as totalRevenue,
            COUNT(mpo.id) as totalOrders
        FROM mini_warehouse mw
        LEFT JOIN mini_parking_order mpo ON mw.id = mpo.warehouse_id
            AND mpo.delete_flag = 0
            AND mpo.pay_status = 2
            AND DATE(mpo.create_time) = #{date}
        WHERE mw.delete_flag = 0
        GROUP BY mw.id, mw.warehouse_name
        ORDER BY totalRevenue DESC, totalOrders DESC
        LIMIT 5
    </select>

</mapper>

