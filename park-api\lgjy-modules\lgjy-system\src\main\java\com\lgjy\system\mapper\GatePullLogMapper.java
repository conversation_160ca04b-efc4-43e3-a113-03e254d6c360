package com.lgjy.system.mapper;

import java.util.List;
import com.lgjy.system.domain.GatePullLog;

/**
 * 道闸推送日志Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-08-03
 */
public interface GatePullLogMapper 
{
    /**
     * 查询道闸推送日志
     * 
     * @param id 道闸推送日志主键
     * @return 道闸推送日志
     */
    public GatePullLog selectGatePullLogById(String id);

    /**
     * 查询道闸推送日志列表
     * 
     * @param gatePullLog 道闸推送日志
     * @return 道闸推送日志集合
     */
    public List<GatePullLog> selectGatePullLogList(GatePullLog gatePullLog);

    /**
     * 删除道闸推送日志
     * 
     * @param id 道闸推送日志主键
     * @return 结果
     */
    public int deleteGatePullLogById(String id);

    /**
     * 批量删除道闸推送日志
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteGatePullLogByIds(String[] ids);

    /**
     * 清空道闸推送日志
     */
    public void cleanGatePullLog();
}
