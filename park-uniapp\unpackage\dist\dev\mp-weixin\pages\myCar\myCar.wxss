/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* uni.scss */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.my-car-container.data-v-091d73e0 {
  background-color: #f5f5f5;
  padding: 25rpx 25rpx 140rpx 25rpx;
  min-height: 100vh;
  position: relative;
}
.my-car-list .my-car-item.data-v-091d73e0 {
  background: #fff;
  border-radius: 24rpx;
  padding: 32rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}
.my-car-list .my-car-item .my-car-content.data-v-091d73e0 {
  display: flex;
  align-items: center;
}
.my-car-list .my-car-item .my-car-content .my-car-content-left.data-v-091d73e0 {
  margin-right: 150rpx;
}
.my-car-list .my-car-item .my-car-content .my-car-content-left .plateNumber.data-v-091d73e0 {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 12rpx;
}
.my-car-list .my-car-item .my-car-content .my-car-content-left .car-info.data-v-091d73e0 {
  display: flex;
  align-items: center;
  gap: 8rpx;
}
.my-car-list .my-car-item .my-car-content .my-car-content-left .car-info .carType.data-v-091d73e0,
.my-car-list .my-car-item .my-car-content .my-car-content-left .car-info .energyType.data-v-091d73e0 {
  font-size: 28rpx;
  color: #666;
}
.my-car-list .my-car-item .my-car-content .my-car-content-left .car-info .divider.data-v-091d73e0 {
  color: #ccc;
  font-size: 24rpx;
}
.my-car-list .my-car-item .my-car-content .my-car-content-right .car-image.data-v-091d73e0 {
  width: 250rpx;
  height: 140rpx;
}
.my-car-list .my-car-item .my-car-item-bottom.data-v-091d73e0 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 24rpx;
  border-top: 2rpx solid #f0f0f0;
}
.my-car-list .my-car-item .my-car-item-bottom .default-section.data-v-091d73e0 {
  display: flex;
  align-items: center;
}
.my-car-list .my-car-item .my-car-item-bottom .default-section .radio-custom.data-v-091d73e0 {
  transform: scale(0.9);
}
.my-car-list .my-car-item .my-car-item-bottom .default-section .default-text.data-v-091d73e0 {
  font-size: 28rpx;
  color: #666;
}
.my-car-list .my-car-item .my-car-item-bottom .action-section.data-v-091d73e0 {
  display: flex;
  align-items: center;
  gap: 24rpx;
}
.empty-state.data-v-091d73e0 {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 0;
}
.empty-state .empty-image.data-v-091d73e0 {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 32rpx;
  opacity: 0.6;
}
.empty-state .empty-text.data-v-091d73e0 {
  font-size: 32rpx;
  color: #999;
  margin-bottom: 48rpx;
}

/* 底部固定的添加车辆按钮 */
.bottom-add-btn.data-v-091d73e0 {
  position: fixed;
  bottom: 40rpx;
  width: 85%;
  left: 50%;
  transform: translateX(-50%);
  background: linear-gradient(90deg, #4BA1FC 0%, #9b8eff 100%);
  color: #fff;
  padding: 22rpx;
  border-radius: 50rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 999;
}
.bottom-add-btn .add-icon.data-v-091d73e0 {
  font-size: 36rpx;
  font-weight: bold;
  margin-right: 12rpx;
}
.bottom-add-btn .add-text.data-v-091d73e0 {
  font-size: 32rpx;
  font-weight: 500;
}