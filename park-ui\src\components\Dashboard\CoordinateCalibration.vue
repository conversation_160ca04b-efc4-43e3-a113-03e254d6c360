<template>
  <!-- 坐标校准测试组件 -->
  <div class="coordinate-calibration">
    <div class="calibration-panel">
      <h4 class="panel-title">坐标校准测试工具</h4>
      
      <!-- 坐标转换模式选择 -->
      <div class="mode-section">
        <div class="section-title">坐标转换模式：</div>
        <div class="mode-buttons">
          <button
            class="mode-button"
            :class="{ active: coordinateMode === 'original' }"
            @click="switchCoordinateMode('original')"
          >
            原始坐标
          </button>
          <button
            class="mode-button"
            :class="{ active: coordinateMode === 'wgs84ToGcj02' }"
            @click="switchCoordinateMode('wgs84ToGcj02')"
          >
            WGS84→GCJ02
          </button>
          <button
            class="mode-button"
            :class="{ active: coordinateMode === 'gcj02ToWgs84' }"
            @click="switchCoordinateMode('gcj02ToWgs84')"
          >
            GCJ02→WGS84
          </button>
          <button
            class="mode-button"
            :class="{ active: coordinateMode === 'customCorrection' }"
            @click="switchCoordinateMode('customCorrection')"
          >
            实测校正
          </button>
        </div>
      </div>
      
      <!-- 鼠标坐标显示 -->
      <div class="coordinate-info">
        <div class="info-title">鼠标坐标：</div>
        <div class="info-content">{{ mouseCoordinates }}</div>
      </div>
      
      <!-- 测试功能按钮 -->
      <div class="test-controls">
        <button class="test-button calibrate-btn" @click="enableCalibration">
          {{ calibrationMode ? '关闭校准模式' : '坐标校准模式' }}
        </button>
        <button class="test-button locate-btn" @click="locateZhuyuyuan">
          定位著雨苑
        </button>
        <button class="test-button analyze-btn" @click="analyzeCoordinateOffset">
          坐标偏移分析
        </button>
      </div>
      
      <!-- 校准说明 -->
      <div class="calibration-info">
        <div class="info-title">使用说明：</div>
        <div class="info-text">
          <p>1. 选择不同的坐标转换模式</p>
          <p>2. 对比鼠标坐标与地图左下角坐标</p>
          <p>3. 使用"定位著雨苑"测试准确性</p>
          <p>4. "实测校正"模式精度最高</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, inject } from 'vue'

// 注入地图相关数据和方法
const coordinateMode = inject('coordinateMode')
const mouseCoordinates = inject('mouseCoordinates')
const calibrationMode = inject('calibrationMode')
const switchCoordinateMode = inject('switchCoordinateMode')
const enableCalibration = inject('enableCalibration')
const locateZhuyuyuan = inject('locateZhuyuyuan')
const analyzeCoordinateOffset = inject('analyzeCoordinateOffset')
</script>

<style scoped>
.coordinate-calibration {
  position: absolute;
  top: 20px;
  right: 20px;
  z-index: 1000;
  width: 280px;
}

.calibration-panel {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 12px;
  padding: 16px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  backdrop-filter: blur(10px);
}

.panel-title {
  margin: 0 0 16px 0;
  font-size: 14px;
  font-weight: 600;
  color: #2B6CB0;
  text-align: center;
  border-bottom: 2px solid #e0e0e0;
  padding-bottom: 8px;
}

.mode-section {
  margin-bottom: 16px;
}

.section-title {
  font-size: 12px;
  font-weight: 600;
  color: #333;
  margin-bottom: 8px;
}

.mode-buttons {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 6px;
}

.mode-button {
  background: rgba(255, 255, 255, 0.95);
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  padding: 8px 6px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 10px;
  font-weight: 500;
  color: #666;
  text-align: center;
}

.mode-button:hover {
  border-color: #2B6CB0;
  background: rgba(43, 108, 176, 0.05);
  color: #2B6CB0;
}

.mode-button.active {
  background: #2B6CB0;
  border-color: #2B6CB0;
  color: white;
}

.coordinate-info {
  margin-bottom: 16px;
  padding: 10px;
  background: #f8f9fa;
  border-radius: 6px;
}

.info-title {
  font-size: 11px;
  font-weight: 600;
  color: #333;
  margin-bottom: 4px;
}

.info-content {
  font-size: 11px;
  color: #666;
  font-family: monospace;
  background: white;
  padding: 4px 6px;
  border-radius: 4px;
  border: 1px solid #e0e0e0;
}

.test-controls {
  margin-bottom: 16px;
}

.test-button {
  width: 100%;
  border: none;
  border-radius: 6px;
  padding: 8px 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 11px;
  font-weight: 500;
  color: white;
  margin-bottom: 6px;
}

.calibrate-btn {
  background: #28a745;
}

.calibrate-btn:hover {
  background: #218838;
}

.locate-btn {
  background: #007bff;
}

.locate-btn:hover {
  background: #0056b3;
}

.analyze-btn {
  background: #6f42c1;
}

.analyze-btn:hover {
  background: #5a32a3;
}

.calibration-info {
  background: #e7f3ff;
  padding: 10px;
  border-radius: 6px;
  border-left: 4px solid #2B6CB0;
}

.info-text {
  font-size: 10px;
  line-height: 1.4;
  color: #495057;
}

.info-text p {
  margin: 0 0 4px 0;
}

.info-text p:last-child {
  margin-bottom: 0;
}
</style>
