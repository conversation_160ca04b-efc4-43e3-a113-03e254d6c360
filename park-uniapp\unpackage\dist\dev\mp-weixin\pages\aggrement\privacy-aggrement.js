"use strict";
const common_vendor = require("../../common/vendor.js");
const api_agreement = require("../../api/agreement.js");
const _sfc_main = {
  __name: "privacy-aggrement",
  setup(__props) {
    const loading = common_vendor.ref(true);
    const error = common_vendor.ref("");
    const agreementData = common_vendor.ref({});
    const loadAgreement = async () => {
      try {
        loading.value = true;
        error.value = "";
        const response = await api_agreement.getAgreementByType(1);
        if (response.code === 200) {
          agreementData.value = response.data || {};
        } else {
          error.value = response.msg || "获取协议失败";
        }
      } catch (err) {
        console.error("加载隐私政策失败:", err);
        error.value = "网络错误，请稍后重试";
      } finally {
        loading.value = false;
      }
    };
    const goBack = () => {
      common_vendor.index.navigateBack();
    };
    common_vendor.onLoad(() => {
      loadAgreement();
    });
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_vendor.o(goBack),
        b: loading.value
      }, loading.value ? {} : error.value ? {
        d: common_vendor.t(error.value),
        e: common_vendor.o(loadAgreement)
      } : {
        f: agreementData.value.agreementContent || "暂无内容"
      }, {
        c: error.value
      });
    };
  }
};
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-3f90bf6c"]]);
wx.createPage(MiniProgramPage);
