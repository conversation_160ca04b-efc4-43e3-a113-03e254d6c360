/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* uni.scss */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.u-empty.data-v-d5274fb5,
.u-empty__wrap.data-v-d5274fb5,
.u-tabs.data-v-d5274fb5,
.u-tabs__wrapper.data-v-d5274fb5,
.u-tabs__wrapper__scroll-view-wrapper.data-v-d5274fb5,
.u-tabs__wrapper__scroll-view.data-v-d5274fb5,
.u-tabs__wrapper__nav.data-v-d5274fb5,
.u-tabs__wrapper__nav__line.data-v-d5274fb5,
.up-empty.data-v-d5274fb5,
.up-empty__wrap.data-v-d5274fb5,
.up-tabs.data-v-d5274fb5,
.up-tabs__wrapper.data-v-d5274fb5,
.up-tabs__wrapper__scroll-view-wrapper.data-v-d5274fb5,
.up-tabs__wrapper__scroll-view.data-v-d5274fb5,
.up-tabs__wrapper__nav.data-v-d5274fb5,
.up-tabs__wrapper__nav__line.data-v-d5274fb5 {
  display: flex;
  flex-direction: column;
  flex-shrink: 0;
  flex-grow: 0;
  flex-basis: auto;
  align-items: stretch;
  align-content: flex-start;
}
.u-grid-item.data-v-d5274fb5 {
  align-items: center;
  justify-content: center;
  position: relative;
  flex-direction: column;
  box-sizing: border-box;
  display: flex;
  position: relative;
  float: left;
  margin-top: 1rpx;
}
.u-grid-item--hover-class.data-v-d5274fb5 {
  opacity: 0.5;
}