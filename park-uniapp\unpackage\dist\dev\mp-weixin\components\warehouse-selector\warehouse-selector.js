"use strict";
const common_vendor = require("../../common/vendor.js");
if (!Array) {
  const _easycom_up_icon2 = common_vendor.resolveComponent("up-icon");
  const _easycom_up_popup2 = common_vendor.resolveComponent("up-popup");
  (_easycom_up_icon2 + _easycom_up_popup2)();
}
const _easycom_up_icon = () => "../../node-modules/uview-plus/components/u-icon/u-icon.js";
const _easycom_up_popup = () => "../../node-modules/uview-plus/components/u-popup/u-popup.js";
if (!Math) {
  (_easycom_up_icon + _easycom_up_popup)();
}
const _sfc_main = {
  __name: "warehouse-selector",
  props: {
    show: {
      type: Boolean,
      default: false
    },
    warehouseList: {
      type: Array,
      default: () => []
    },
    currentWarehouse: {
      type: Object,
      default: () => ({})
    },
    windowHeightHalf: {
      type: Number,
      default: 400
    }
  },
  emits: ["close", "select"],
  setup(__props, { emit: __emit }) {
    const emit = __emit;
    const handleClose = () => {
      emit("close");
    };
    const handleSelect = (warehouse) => {
      emit("select", warehouse);
    };
    return (_ctx, _cache) => {
      return {
        a: common_vendor.o(handleClose),
        b: common_vendor.p({
          name: "close",
          size: "18",
          color: "#999"
        }),
        c: common_vendor.f(__props.warehouseList, (warehouse, k0, i0) => {
          return common_vendor.e({
            a: common_vendor.t(warehouse.name),
            b: warehouse.id === __props.currentWarehouse.id
          }, warehouse.id === __props.currentWarehouse.id ? {
            c: "e7c75adf-2-" + i0 + ",e7c75adf-0",
            d: common_vendor.p({
              name: "checkmark",
              size: "16",
              color: "#40a9ff"
            })
          } : {}, {
            e: warehouse.id,
            f: warehouse.id === __props.currentWarehouse.id ? 1 : "",
            g: common_vendor.o(($event) => handleSelect(warehouse), warehouse.id)
          });
        }),
        d: __props.windowHeightHalf + "px",
        e: common_vendor.o(handleClose),
        f: common_vendor.p({
          show: __props.show,
          mode: "bottom",
          round: "20",
          safeAreaInsetBottom: true
        })
      };
    };
  }
};
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-e7c75adf"]]);
wx.createComponent(Component);
