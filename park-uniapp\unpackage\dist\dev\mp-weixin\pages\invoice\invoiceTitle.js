"use strict";
const common_vendor = require("../../common/vendor.js");
const api_invoice = require("../../api/invoice.js");
if (!Array) {
  const _easycom_up_icon2 = common_vendor.resolveComponent("up-icon");
  const _easycom_up_empty2 = common_vendor.resolveComponent("up-empty");
  (_easycom_up_icon2 + _easycom_up_empty2)();
}
const _easycom_up_icon = () => "../../node-modules/uview-plus/components/u-icon/u-icon.js";
const _easycom_up_empty = () => "../../node-modules/uview-plus/components/u-empty/u-empty.js";
if (!Math) {
  (_easycom_up_icon + _easycom_up_empty)();
}
const _sfc_main = {
  __name: "invoiceTitle",
  setup(__props) {
    const invoiceTitleList = common_vendor.ref([]);
    common_vendor.onShow(() => {
      fetchInvoiceTitleList();
    });
    const getTypeIcon = (item) => {
      if (item.invoiceType === 1) {
        return "file-text";
      }
      return item.titleType === 1 ? "home" : "account";
    };
    const getTypeIconColor = (item) => {
      if (item.invoiceType === 1) {
        return "#4BA1FC";
      }
      return item.titleType === 1 ? "#FF9500" : "#34C759";
    };
    const getTypeText = (item) => {
      if (item.invoiceType === 1) {
        return "专用发票抬头";
      }
      const typeText = item.titleType === 1 ? "公司" : "个人";
      return `${typeText} · 普通发票抬头`;
    };
    const fetchInvoiceTitleList = () => {
      api_invoice.getInvoiceTitleList().then((res) => {
        console.log(res);
        invoiceTitleList.value = [];
        common_vendor.nextTick$1(() => {
          invoiceTitleList.value = res.data || [];
        });
      }).catch((err) => {
        console.error("获取发票抬头列表失败:", err);
        common_vendor.index.showToast({
          title: "获取数据失败",
          icon: "none"
        });
      });
    };
    const addInvoiceTitle = () => {
      common_vendor.index.navigateTo({
        url: "/pages/invoice/addInvoiceTitle?isEdit=false"
      });
    };
    const handleEdit = (item) => {
      const obj = JSON.stringify(item);
      common_vendor.index.navigateTo({
        url: "/pages/invoice/addInvoiceTitle?isEdit=true&obj=" + encodeURIComponent(obj)
      });
    };
    const handleDelete = (item) => {
      common_vendor.index.showModal({
        content: "确定要删除这个发票抬头吗？",
        cancelText: "取消",
        confirmText: "确认",
        success: (res) => {
          if (res.confirm) {
            common_vendor.index.showLoading({
              title: "删除中..."
            });
            api_invoice.deleteInvoiceTitle({ id: item.id }).then((res2) => {
              common_vendor.index.hideLoading();
              if (res2.code === 200) {
                common_vendor.index.showToast({
                  title: "删除成功",
                  icon: "success"
                });
                fetchInvoiceTitleList();
              } else {
                common_vendor.index.showToast({
                  title: res2.msg || "删除失败",
                  icon: "none"
                });
              }
            }).catch((err) => {
              common_vendor.index.hideLoading();
              console.error("删除发票抬头失败:", err);
              common_vendor.index.showToast({
                title: "删除失败，请重试",
                icon: "none"
              });
            });
          }
        }
      });
    };
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: invoiceTitleList.value.length > 0
      }, invoiceTitleList.value.length > 0 ? {
        b: common_vendor.f(invoiceTitleList.value, (item, k0, i0) => {
          return {
            a: "59070f7b-0-" + i0,
            b: common_vendor.p({
              name: getTypeIcon(item),
              color: getTypeIconColor(item),
              size: "16"
            }),
            c: common_vendor.t(getTypeText(item)),
            d: "59070f7b-1-" + i0,
            e: common_vendor.o(($event) => handleEdit(item), item.id),
            f: "59070f7b-2-" + i0,
            g: common_vendor.o(($event) => handleDelete(item), item.id),
            h: common_vendor.t(item.invoiceTitleContent),
            i: item.id
          };
        }),
        c: common_vendor.p({
          name: "edit-pen",
          color: "#9e9e9e",
          size: "16"
        }),
        d: common_vendor.p({
          name: "trash",
          color: "#9e9e9e",
          size: "16"
        })
      } : {
        e: common_vendor.p({
          text: "暂无发票抬头信息"
        })
      }, {
        f: common_vendor.p({
          name: "plus",
          color: "#fff",
          size: "18"
        }),
        g: common_vendor.o(addInvoiceTitle)
      });
    };
  }
};
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-59070f7b"]]);
wx.createPage(MiniProgramPage);
