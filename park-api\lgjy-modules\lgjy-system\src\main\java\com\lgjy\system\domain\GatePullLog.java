package com.lgjy.system.domain;

import com.lgjy.common.core.annotation.Excel;
import com.lgjy.common.core.web.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 道闸推送日志对象 gate_pull_log
 * 
 * <AUTHOR>
 * @date 2025-08-03
 */
public class GatePullLog extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private String id;

    /** 停车场名称 */
    @Excel(name = "停车场名称")
    private String parkingName;

    /** 访问地址 */
    @Excel(name = "访问地址")
    private String accessAddress;

    /** 接口地址 */
    @Excel(name = "接口地址")
    private String address;

    /** 推送数据 */
    @Excel(name = "推送数据")
    private String data;

    /** 最后更新时间 */
    @Excel(name = "推送时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private String lastUpdate;

    public void setId(String id) 
    {
        this.id = id;
    }

    public String getId() 
    {
        return id;
    }
    public void setParkingName(String parkingName) 
    {
        this.parkingName = parkingName;
    }

    public String getParkingName() 
    {
        return parkingName;
    }
    public void setAccessAddress(String accessAddress) 
    {
        this.accessAddress = accessAddress;
    }

    public String getAccessAddress() 
    {
        return accessAddress;
    }
    public void setAddress(String address) 
    {
        this.address = address;
    }

    public String getAddress() 
    {
        return address;
    }
    public void setData(String data) 
    {
        this.data = data;
    }

    public String getData() 
    {
        return data;
    }
    public void setLastUpdate(String lastUpdate) 
    {
        this.lastUpdate = lastUpdate;
    }

    public String getLastUpdate() 
    {
        return lastUpdate;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("parkingName", getParkingName())
            .append("accessAddress", getAccessAddress())
            .append("address", getAddress())
            .append("data", getData())
            .append("lastUpdate", getLastUpdate())
            .toString();
    }
}
