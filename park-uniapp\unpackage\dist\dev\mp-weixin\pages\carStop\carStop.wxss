/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* uni.scss */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.car-stop-container.data-v-50a9b85c {
  background-color: #f5f5f5;
  height: 100vh;
}
.cell.data-v-50a9b85c {
  padding: 40rpx 32rpx 0;
}
.cell .top-cell.data-v-50a9b85c {
  margin-bottom: 20rpx;
}
.cell .top-cell .top-cell-title.data-v-50a9b85c {
  margin-right: 8rpx;
}
.cell .top-cell .top-cell-title .title.data-v-50a9b85c {
  font-size: 40rpx;
  font-weight: bold;
  color: #212121;
  margin-bottom: 8rpx;
}
.cell .top-cell .top-cell-title .desc.data-v-50a9b85c {
  font-size: 28rpx;
  font-weight: 400;
  color: #9e9e9e;
}
.cell .top-cell image.data-v-50a9b85c {
  width: 284rpx;
  height: 200rpx;
}