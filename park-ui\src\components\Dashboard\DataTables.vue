<template>
  <!-- 双表格组件 -->
  <div class="tables-section">
    <!-- 在停订单Top5 -->
    <div class="table-panel">
      <div class="section-header">
        <h3>💰 在停订单top5</h3>
      </div>
      <div class="ranking-list">
        <div v-for="(item, index) in topOrders" :key="index" class="ranking-item">
          <span class="ranking-name">{{ item.name }}</span>
          <div class="progress-bar">
            <div class="progress-fill" :style="{ width: item.percentage + '%' }"></div>
            <span class="progress-value">{{ item.value }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 场库统计 -->
    <div class="table-panel">
      <div class="section-header">
        <h3>场库统计</h3>
        <div class="time-filter">
          <button class="filter-btn" :class="{ active: timeFilter === 'month' }" @click="handleTimeFilterChange('month')">月</button>
          <button class="filter-btn" :class="{ active: timeFilter === 'year' }" @click="handleTimeFilterChange('year')">年</button>
        </div>
      </div>
      <div class="data-table">
        <div class="table-header">
          <span>场库名称</span>
          <span>订单总数</span>
          <span>订单金额</span>
        </div>
        <div v-for="(item, index) in warehouseStats" :key="index" class="table-row" :class="{ even: index % 2 === 1 }">
          <span class="warehouse-name">{{ item.name }}</span>
          <span class="order-count">{{ item.orders }}</span>
          <span class="order-amount">{{ item.amount }}万元</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'

// 定义props
const props = defineProps({
  topOrders: {
    type: Array,
    default: () => []
  },
  warehouseStats: {
    type: Array,
    default: () => []
  }
})

// 定义emits
const emit = defineEmits(['time-filter-change'])

const timeFilter = ref('month')

// 监听时间筛选变化
const handleTimeFilterChange = (filter) => {
  timeFilter.value = filter
  emit('time-filter-change', filter)
}
</script>

<style scoped>
/* 双表格区域 */
.tables-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.table-panel {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  padding: 20px;
  flex: 1;
}

/* 星空主题表格面板 - 只在暗黑模式下生效 */
html.dark .table-panel {
  background: rgba(255, 255, 255, 0.95) !important;
  border-radius: 16px !important;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3) !important;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 2px solid #e3f2fd;
}

.section-header h3 {
  margin: 0;
  font-size: 16px;
  color: white;
  background: #2B6CB0;
  padding: 8px 16px;
  border-radius: 4px;
  font-weight: 600;
}

/* 时间筛选器 */
.time-filter {
  display: flex;
  gap: 4px;
}

.filter-btn {
  width: 32px;
  height: 32px;
  border: 1px solid #2B6CB0;
  background: white;
  color: #2B6CB0;
  border-radius: 50%;
  cursor: pointer;
  font-size: 12px;
  font-weight: 600;
  transition: all 0.2s ease;
}

.filter-btn.active,
.filter-btn:hover {
  background: #2B6CB0;
  color: white;
}

/* 排行榜列表 */
.ranking-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.ranking-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 0;
}

.ranking-name {
  font-size: 14px;
  color: #333;
  font-weight: 500;
  flex: 0 0 120px;
}

.progress-bar {
  flex: 1;
  position: relative;
  height: 20px;
  background: #f0f0f0;
  border-radius: 10px;
  margin: 0 12px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #2B6CB0 0%, #64B5F6 100%);
  border-radius: 10px;
  transition: width 0.3s ease;
}

.progress-value {
  position: absolute;
  right: 8px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 12px;
  color: white;
  font-weight: 600;
}

/* 数据表格 */
.data-table {
  font-size: 14px;
}

.table-header {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr;
  gap: 12px;
  padding: 12px 0;
  border-bottom: 1px solid #e8f4fd;
  font-weight: 600;
  color: #666;
  background: #f8f9fa;
  border-radius: 4px;
  padding-left: 12px;
  padding-right: 12px;
}

.table-row {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr;
  gap: 12px;
  padding: 12px;
  border-bottom: 1px solid #f0f0f0;
  transition: background-color 0.2s ease;
}

.table-row:hover {
  background: rgba(43, 108, 176, 0.05);
}

.table-row.even {
  background: #f8f9fa;
}

.table-row.even:hover {
  background: rgba(43, 108, 176, 0.08);
}

.warehouse-name {
  color: #333;
  font-weight: 500;
}

.order-count {
  color: #2B6CB0;
  font-weight: 600;
  text-align: center;
}

.order-amount {
  color: #67C23A;
  font-weight: 600;
  text-align: center;
}

/* 响应式设计 */
@media (max-width: 1400px) {
  .tables-section {
    flex-direction: row;
  }
}

@media (max-width: 768px) {
  .tables-section {
    flex-direction: column;
  }

  .table-header,
  .table-row {
    grid-template-columns: 1.5fr 1fr 1fr;
    gap: 8px;
    padding: 8px;
  }

  .ranking-name {
    flex: 0 0 80px;
    font-size: 12px;
  }

  .progress-value {
    font-size: 10px;
  }
}
</style>
