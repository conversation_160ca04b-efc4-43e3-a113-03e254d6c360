<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lgjy.wx.mapper.WxInvoiceRecordMapper">
    <sql id="selectWxInvoiceRecordVo">
        select id, user_id,warehouse_id, function_type, invoice_type, invoice_no, issue_date,
               total_money, invoice_title_content, unit_duty_paragraph, register_address,
               register_phone, deposit_bank, bank_account, notify_mobile_no, notify_email,
               pdf_url, trade_id,status,remark, create_time, update_time,
               reverse_date,qr_code_id,invoice_code,mid,tid,order_date,pdf_preview_url,total_tax,
               reopen_sign,old_trade_id
        from mini_invoice_record
    </sql>

<!--    <select id="selectWxInvoiceRecordById" resultType="com.lgjy.wx.domain.WxInvoiceRecord">-->
<!--        <include refid="selectWxInvoiceRecordVo"/>-->
<!--        where id = #{id}-->
<!--    </select>-->

    <update id="updateWxInvoiceRecord">
        update mini_invoice_record
        <trim prefix="SET" suffixOverrides=",">
            <if test="userId != null">user_id = #{userId},</if>
            <if test="functionType != null and functionType != ''">function_type = #{functionType},</if>
            <if test="invoiceType != null and invoiceType != ''">invoice_type = #{invoiceType},</if>
            <if test="invoiceNo != null">invoice_no = #{invoiceNo},</if>
            <if test="issueDate != null">issue_date = #{issueDate},</if>
            <if test="totalMoney != null">total_money = #{totalMoney},</if>
            <if test="invoiceTitleContent != null and invoiceTitleContent != ''">invoice_title_content =
                #{invoiceTitleContent},
            </if>
            <if test="unitDutyParagraph != null">unit_duty_paragraph = #{unitDutyParagraph},</if>
            <if test="registerAddress != null">register_address = #{registerAddress},</if>
            <if test="registerPhone != null">register_phone = #{registerPhone},</if>
            <if test="depositBank != null">deposit_bank = #{depositBank},</if>
            <if test="bankAccount != null">bank_account = #{bankAccount},</if>
            <if test="notifyMobileNo != null">notify_mobile_no = #{notifyMobileNo},</if>
            <if test="notifyEmail != null">notify_email = #{notifyEmail},</if>
            <if test="pdfUrl != null">pdf_url = #{pdfUrl},</if>
            <if test="tradeId != null">trade_id = #{tradeId},</if>
            <if test="status != null">status = #{status},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="reverseDate != null">reverse_date = #{reverseDate},</if>
            <if test="qrCodeId != null">qr_code_id = #{qrCodeId},</if>
            <if test="invoiceCode != null">invoice_code = #{invoiceCode},</if>
            <if test="mid != null">mid = #{mid},</if>
            <if test="tid != null">tid = #{tid},</if>
            <if test="orderDate != null">order_date = #{orderDate},</if>
            <if test="pdfPreviewUrl != null">pdf_preview_url = #{pdfPreviewUrl},</if>
            <if test="totalTax != null">total_tax = #{totalTax},</if>
            <if test="reopenSign != null">reopen_sign = #{reopenSign},</if>
            <if test="oldTradeId != null">old_trade_id = #{oldTradeId},</if>
            update_time = sysdate()
        </trim>
        where id = #{id}
    </update>

    <insert id="insertWxInvoiceRecord">
        insert into mini_invoice_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="userId != null">user_id,</if>
            <if test="warehouseId != null">warehouse_id,</if>
            <if test="functionType != null and functionType != ''">function_type,</if>
            <if test="invoiceType != null">invoice_type,</if>
            <if test="invoiceNo != null">invoice_no,</if>
            <if test="issueDate != null">issue_date,</if>
            <if test="totalMoney != null">total_money,</if>
            <if test="invoiceTitleContent != null and invoiceTitleContent != ''">invoice_title_content,</if>
            <if test="unitDutyParagraph != null">unit_duty_paragraph,</if>
            <if test="registerAddress != null">register_address,</if>
            <if test="registerPhone != null">register_phone,</if>
            <if test="depositBank != null">deposit_bank,</if>
            <if test="bankAccount != null">bank_account,</if>
            <if test="notifyMobileNo != null">notify_mobile_no,</if>
            <if test="notifyEmail != null">notify_email,</if>
            <if test="pdfUrl != null">pdf_url,</if>
            <if test="tradeId != null">trade_id,</if>
            <if test="status != null">status,</if>
            <if test="remark != null">remark,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="reverseDate != null">reverse_date,</if>
            <if test="qrCodeId != null">qr_code_id,</if>
            <if test="invoiceCode != null">invoice_code,</if>
            <if test="mid != null">mid,</if>
            <if test="tid != null">tid,</if>
            <if test="orderDate != null">order_date,</if>
            <if test="pdfPreviewUrl != null">pdf_preview_url,</if>
            <if test="totalTax != null">total_tax,</if>
            <if test="reopenSign != null">reopen_sign,</if>
            <if test="oldTradeId != null">old_trade_id,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="userId != null">#{userId},</if>
            <if test="warehouseId != null">#{warehouseId},</if>
            <if test="functionType != null and functionType != ''">#{functionType},</if>
            <if test="invoiceType != null">#{invoiceType},</if>
            <if test="invoiceNo != null">#{invoiceNo},</if>
            <if test="issueDate != null">#{issueDate},</if>
            <if test="totalMoney != null">#{totalMoney},</if>
            <if test="invoiceTitleContent != null and invoiceTitleContent != ''">#{invoiceTitleContent},</if>
            <if test="unitDutyParagraph != null">#{unitDutyParagraph},</if>
            <if test="registerAddress != null">#{registerAddress},</if>
            <if test="registerPhone != null">#{registerPhone},</if>
            <if test="depositBank != null">#{depositBank},</if>
            <if test="bankAccount != null">#{bankAccount},</if>
            <if test="notifyMobileNo != null">#{notifyMobileNo},</if>
            <if test="notifyEmail != null">#{notifyEmail},</if>
            <if test="pdfUrl != null">#{pdfUrl},</if>
            <if test="tradeId != null">#{tradeId},</if>
            <if test="status != null">#{status},</if>
            <if test="remark != null">#{remark},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="reverseDate != null">#{reverseDate},</if>
            <if test="qrCodeId != null">#{qrCodeId},</if>
            <if test="invoiceCode != null">#{invoiceCode},</if>
            <if test="mid != null">#{mid},</if>
            <if test="tid != null">#{tid},</if>
            <if test="orderDate != null">#{orderDate},</if>
            <if test="pdfPreviewUrl != null">#{pdfPreviewUrl},</if>
            <if test="totalTax != null">#{totalTax},</if>
            <if test="reopenSign != null">#{reopenSign},</if>
            <if test="oldTradeId != null">#{oldTradeId},</if>
        </trim>
    </insert>

    <select id="selectWxInvoiceRecordList" resultType="com.lgjy.wx.domain.WxInvoiceRecord">
        <include refid="selectWxInvoiceRecordVo"/>
        <where>
            <if test="userId != null ">and user_id = #{userId}</if>
            <if test="warehouseId != null ">and warehouse_id = #{warehouseId}</if>
            <if test="invoiceType != null  and invoiceType != ''">and invoice_type = #{invoiceType}</if>
            <if test="functionType != null  and functionType != ''">and function_type = #{functionType}</if>
            <if test="tradeId != null ">and trade_id = #{tradeId}</if>
            <if test="oldTradeId != null ">and old_trade_id = #{oldTradeId}</if>
        </where>
        order by update_time desc
    </select>

    <select id="selectWxInvoiceRecordById" resultType="com.lgjy.wx.domain.WxInvoiceRecord">
        <include refid="selectWxInvoiceRecordVo"/>
        where id = #{id}
    </select>

    <select id="queryInvoiceRecordByTradeId" resultType="com.lgjy.wx.domain.WxInvoiceRecord">
        <include refid="selectWxInvoiceRecordVo"/>
        where trade_id = #{tradeId}
    </select>

    <select id="queryInvoiceRecordByOldTradeId" resultType="com.lgjy.wx.domain.WxInvoiceRecord">
        <include refid="selectWxInvoiceRecordVo"/>
        where old_trade_id = #{tradeId}
    </select>
</mapper>