<template>
  <div class="top-stats-section">
    <div class="stat-card">
      <div class="stat-icon">
        <img :src="warehouseIconUrl" alt="场库图标" width="56" height="56" />
      </div>
      <div class="stat-content">
        <div class="stat-number">{{ statisticsData.totalWarehouses }}</div>
        <div class="stat-label">总场库数量(个)</div>
      </div>
    </div>

    <div class="stat-card">
      <div class="stat-icon">
        <img :src="parkIconUrl" alt="车位图标" width="56" height="56" />
      </div>
      <div class="stat-content">
        <div class="stat-number">{{ statisticsData.totalParkingSpaces }}</div>
        <div class="stat-label">总车位数量(个)</div>
      </div>
    </div>

    <div class="stat-card">
      <div class="stat-icon">
        <img :src="wxUserIconUrl" alt="用户图标" width="56" height="56" />
      </div>
      <div class="stat-content">
        <div class="stat-number">{{ statisticsData.totalAppUsers }}</div>
        <div class="stat-label">小程序用户数</div>
      </div>
    </div>

    <div class="stat-card">
      <div class="stat-icon">
        <img :src="specialMemberIconUrl" alt="会员图标" width="56" height="56" />
      </div>
      <div class="stat-content">
        <div class="stat-number">{{ statisticsData.specialMemberCount }}</div>
        <div class="stat-label">特殊会员数量</div>
      </div>
    </div>

    <div class="stat-card">
      <div class="stat-icon">
        <img :src="carInIconUrl" alt="进场图标" width="56" height="56" />
      </div>
      <div class="stat-content">
        <div class="stat-number">{{ statisticsData.todayEntryCount }}</div>
        <div class="stat-label">当日进场车次</div>
      </div>
    </div>

    <div class="stat-card">
      <div class="stat-icon">
        <img :src="carOutIconUrl" alt="出场图标" width="56" height="56" />
      </div>
      <div class="stat-content">
        <div class="stat-number">{{ statisticsData.todayExitCount }}</div>
        <div class="stat-label">当日出场车次</div>
      </div>
    </div>
  </div>
</template>

<script setup>
// 定义props接收统计数据
const props = defineProps({
  statisticsData: {
    type: Object,
    required: true,
    default: () => ({
      totalWarehouses: 0,
      totalParkingSpaces: 0,
      totalAppUsers: 0,
      specialMemberCount: 0,
      todayEntryCount: 0,
      todayExitCount: 0
    })
  }
})

// 统一图标URL
const parkIconUrl = new URL('@/assets/images/park.png', import.meta.url).href
const wxUserIconUrl = new URL('@/assets/images/wx_user.png', import.meta.url).href
const warehouseIconUrl = new URL('@/assets/images/warehouse.png', import.meta.url).href
const specialMemberIconUrl = new URL('@/assets/images/special_member.png', import.meta.url).href
const carInIconUrl = new URL('@/assets/images/car_in.png', import.meta.url).href
const carOutIconUrl = new URL('@/assets/images/car_out.png', import.meta.url).href
</script>

<style scoped>
/* 顶部数据统计区 */
.top-stats-section {
  display: grid;
  grid-template-columns: repeat(6, 1fr);
  gap: 16px;
  margin-bottom: 24px;
  height: 120px;
  position: relative;
  z-index: 10;
}

.stat-card {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid #e8f4fd;
  display: flex;
  align-items: center;
  transition: all 0.3s ease;
  min-width: 200px;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

/* 星空主题卡片样式 - 只在暗黑模式下生效 */
html.dark .stat-card {
  background: rgba(15, 20, 25, 0.85) !important;
  border-radius: 16px !important;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.5) !important;
  border: 1px solid rgba(100, 200, 255, 0.3) !important;
  backdrop-filter: blur(15px);
  -webkit-backdrop-filter: blur(15px);
  position: relative;
  overflow: hidden;
}

html.dark .stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg,
    transparent,
    rgba(100, 200, 255, 0.1),
    transparent);
  transition: left 0.5s ease;
}

html.dark .stat-card:hover {
  transform: translateY(-6px) !important;
  box-shadow: 0 16px 48px rgba(0, 0, 0, 0.6) !important;
  background: rgba(15, 20, 25, 0.95) !important;
  border-color: rgba(100, 200, 255, 0.5) !important;
}

html.dark .stat-card:hover::before {
  left: 100%;
}

html.dark .stat-card::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image:
    radial-gradient(1px 1px at 20px 30px, rgba(255,255,255,0.3), transparent),
    radial-gradient(1px 1px at 40px 70px, rgba(100,200,255,0.2), transparent),
    radial-gradient(1px 1px at 90px 40px, rgba(255,255,255,0.2), transparent),
    radial-gradient(1px 1px at 130px 80px, rgba(230,230,250,0.2), transparent);
  background-repeat: repeat;
  background-size: 150px 100px;
  animation: cardStars 8s ease-in-out infinite alternate;
  pointer-events: none;
  z-index: 1;
}

@keyframes cardStars {
  0%, 100% {
    opacity: 0.3;
  }
  50% {
    opacity: 0.8;
  }
}

.stat-icon {
  flex: 0 0 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  z-index: 2;
}

.stat-content {
  flex: 0 0 50%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  padding-left: 16px;
  position: relative;
  z-index: 2;
}

.stat-number {
  font-size: 32px;
  font-weight: bold;
  color: #2B6CB0;
  line-height: 1;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: #666;
  font-weight: 500;
  white-space: nowrap;
}

/* 星空主题文字样式 - 只在暗黑模式下生效 */
html.dark .stat-number {
  color: #f8f8ff !important;
  text-shadow: 0 0 10px rgba(248, 248, 255, 0.5);
}

html.dark .stat-label {
  color: #e6e6fa !important;
  text-shadow: 0 0 5px rgba(230, 230, 250, 0.3);
}

/* 响应式设计 */
@media (max-width: 1400px) {
  .top-stats-section {
    grid-template-columns: repeat(3, 1fr);
    gap: 16px;
  }
}

@media (max-width: 768px) {
  .top-stats-section {
    grid-template-columns: repeat(2, 1fr);
    gap: 12px;
    height: auto;
  }

  .stat-card {
    padding: 16px;
  }

  .stat-number {
    font-size: 24px;
  }

  .stat-label {
    font-size: 12px;
  }
}
</style>
