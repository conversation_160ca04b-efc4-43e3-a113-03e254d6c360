/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* uni.scss */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.vip-buy-container.data-v-7857afbc {
  background-color: #f5f5f5;
  min-height: 100vh;
  padding: 20rpx;
  padding-bottom: 40rpx;
}
.notice-card.data-v-7857afbc {
  background: linear-gradient(135deg, #fff3e0 0%, #ffeacb 100%);
  border-radius: 16rpx;
  padding: 24rpx;
  margin-bottom: 20rpx;
}
.notice-card .notice-content.data-v-7857afbc {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12rpx;
}
.notice-card .notice-content .notice-text.data-v-7857afbc {
  font-size: 26rpx;
  color: #e65100;
  line-height: 1.5;
  flex: 1;
}
.form-container.data-v-7857afbc {
  background: #ffffff;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}
.content_item.data-v-7857afbc {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
  transition: all 0.2s ease;
}
.content_item.data-v-7857afbc:last-child {
  border-bottom: none;
}
.title.data-v-7857afbc {
  display: flex;
  align-items: center;
  font-size: 28rpx;
  color: #333;
  min-width: 160rpx;
}
.title image.data-v-7857afbc {
  width: 32rpx;
  height: 32rpx;
  margin-right: 15rpx;
}
.word.data-v-7857afbc {
  font-size: 28rpx;
  color: #666;
  text-align: right;
  flex: 1;
}
.word.red.data-v-7857afbc {
  color: #ff4757;
  font-weight: bold;
}
.word.money.data-v-7857afbc {
  font-size: 32rpx;
  font-weight: bold;
  color: #ff4757;
}
.word.clickable-text.data-v-7857afbc {
  color: #4BA1FC;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  text-decoration: underline;
  -webkit-text-decoration-color: rgba(75, 161, 252, 0.3);
          text-decoration-color: rgba(75, 161, 252, 0.3);
  text-underline-offset: 4rpx;
}
.word .picker-display.data-v-7857afbc {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  width: 100%;
  color: inherit;
}
.tips.data-v-7857afbc {
  font-size: 24rpx;
}
.tips.red.data-v-7857afbc {
  color: #ff4757;
}
.button-section.data-v-7857afbc {
  margin-top: 40rpx;
}
.button-section .pay-button.data-v-7857afbc {
  width: 100%;
  background: linear-gradient(90deg, #4BA1FC 0%, #7e6dff 100%);
  border-radius: 44rpx;
  color: #fff;
  font-size: 32rpx;
  font-weight: bold;
  height: 88rpx;
  line-height: 88rpx;
  border: none;
  box-shadow: 0 8rpx 20rpx rgba(102, 126, 234, 0.3);
}
.button-section .pay-button.data-v-7857afbc:active {
  transform: translateY(1rpx);
  box-shadow: 0 4rpx 10rpx rgba(102, 126, 234, 0.3);
}
.button-section .pay-button[disabled].data-v-7857afbc {
  background: #ccc;
  color: #999;
  box-shadow: none;
}
.package-popup.data-v-7857afbc {
  background: #fff;
}
.package-popup .popup-header.data-v-7857afbc {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx 40rpx;
  border-bottom: 1rpx solid #f0f0f0;
}
.package-popup .popup-header .popup-title.data-v-7857afbc {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
}
.package-popup .package-list .package-item.data-v-7857afbc {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx 40rpx;
  border-bottom: 1rpx solid #f0f0f0;
  transition: background-color 0.2s;
}
.package-popup .package-list .package-item.data-v-7857afbc:last-child {
  border-bottom: none;
}
.package-popup .package-list .package-item.data-v-7857afbc:active {
  background-color: #f8f9fa;
}
.package-popup .package-list .package-item.active.data-v-7857afbc {
  background-color: #f0f8ff;
}
.package-popup .package-list .package-item .package-item-left.data-v-7857afbc {
  display: flex;
  flex-direction: column;
}
.package-popup .package-list .package-item .package-item-left .package-item-name.data-v-7857afbc {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
  margin-bottom: 8rpx;
}
.package-popup .package-list .package-item .package-item-left .package-item-days.data-v-7857afbc {
  font-size: 24rpx;
  color: #666;
}
.package-popup .package-list .package-item .package-item-right.data-v-7857afbc {
  display: flex;
  align-items: center;
  gap: 16rpx;
}
.package-popup .package-list .package-item .package-item-right .package-item-price.data-v-7857afbc {
  font-size: 28rpx;
  color: #ff4757;
  font-weight: bold;
}