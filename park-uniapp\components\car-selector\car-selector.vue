<template>
    <u-popup :show="show" @close="handleClose" mode="bottom">
        <view class="car-selector-dropdown">
            <view class="dropdown-header">
                <text class="dropdown-title">选择车辆</text>
                <u-icon name="close" @click="handleClose" size="20" color="#666"></u-icon>
            </view>
            <view class="dropdown-content">
                <view v-for="(car, index) in carList" :key="index" 
                      class="dropdown-item" @tap="toggleCarSelection(car)">
                    <text class="car-plate">{{ car.plateNo }}</text>
                    <u-icon :name="isCarSelected(car) ? 'checkmark-circle-fill' : 'checkmark-circle'" 
                           :color="isCarSelected(car) ? '#4BA1FC' : '#c8c9cc'" size="20"></u-icon>
                </view>
            </view>
            <view class="dropdown-footer">
                <button class="confirm-btn" @tap="confirmSelection">
                    确定选择 ({{ selectedCars.length }}/{{ maxSelect }})
                </button>
            </view>
        </view>
    </u-popup>
</template>

<script setup name="car-selector">
import { ref, watch } from 'vue';

// Props
const props = defineProps({
    show: {
        type: Boolean,
        default: false
    },
    carList: {
        type: Array,
        default: () => []
    },
    maxSelect: {
        type: Number,
        default: 1
    },
    modelValue: {
        type: Array,
        default: () => []
    }
});

// Emits
const emit = defineEmits(['update:modelValue', 'close', 'confirm']);

// 内部选中状态
const selectedCars = ref([]);

// 监听外部传入的选中车辆，同步到内部状态
watch(() => props.modelValue, (newVal) => {
    selectedCars.value = [...newVal];
}, { immediate: true });

// 切换车辆选中状态
const toggleCarSelection = (car) => {
    const index = selectedCars.value.findIndex(item => item.plateNo === car.plateNo);
    
    if (index > -1) {
        // 已选中，取消选择
        selectedCars.value.splice(index, 1);
    } else {
        // 未选中，检查是否超过限制
        if (selectedCars.value.length >= props.maxSelect) {
            if (props.maxSelect === 1) {
                // 单选模式，直接替换
                selectedCars.value = [car];
            } else {
                // 多选模式，提示超过限制
                uni.showToast({
                    title: `最多只能选择${props.maxSelect}辆车`,
                    icon: 'none',
                    duration: 2000
                });
                return;
            }
        } else {
            // 添加到选中列表
            selectedCars.value.push(car);
        }
    }
};

// 检查车辆是否已选中
const isCarSelected = (car) => {
    return selectedCars.value.some(item => item.plateNo === car.plateNo);
};

// 确认选择
const confirmSelection = () => {
    if (selectedCars.value.length === 0) {
        uni.showToast({
            title: '请至少选择一辆车',
            icon: 'none',
            duration: 2000
        });
        return;
    }
    
    // 更新外部数据
    emit('update:modelValue', selectedCars.value);
    emit('confirm', selectedCars.value);
    emit('close');
};

// 关闭选择器
const handleClose = () => {
    emit('close');
};
</script>

<style lang="scss" scoped>
.car-selector-dropdown {
    background: #fff;
    border-radius: 20rpx 20rpx 0 0;
    max-height: 60vh;
    display: flex;
    flex-direction: column;
    
    .dropdown-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 30rpx 40rpx;
        border-bottom: 1rpx solid #f0f0f0;
        
        .dropdown-title {
            font-size: 32rpx;
            font-weight: bold;
            color: #333;
        }
    }
    
    .dropdown-content {
        flex: 1;
        overflow-y: auto;
        padding: 0 40rpx;
        max-height: 400rpx;
        
        .dropdown-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 25rpx 0;
            border-bottom: 1rpx solid #f8f8f8;
            
            &:last-child {
                border-bottom: none;
            }
            
            .car-plate {
                font-size: 28rpx;
                color: #333;
                flex: 1;
            }
        }
    }
    
    .dropdown-footer {
        padding: 30rpx 40rpx;
        border-top: 1rpx solid #f0f0f0;
        
        .confirm-btn {
            width: 100%;
            height: 80rpx;
            background: linear-gradient(90deg, #4BA1FC 0%, #7e6dff 100%);
            color: #fff;
            border: none;
            border-radius: 40rpx;
            font-size: 28rpx;
            font-weight: bold;
        }
    }
}
</style>