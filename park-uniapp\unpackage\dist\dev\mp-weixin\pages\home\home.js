"use strict";
const common_vendor = require("../../common/vendor.js");
const common_assets = require("../../common/assets.js");
const api_warehouse = require("../../api/warehouse.js");
const api_advertConifg = require("../../api/advertConifg.js");
if (!Array) {
  const _easycom_up_swiper2 = common_vendor.resolveComponent("up-swiper");
  _easycom_up_swiper2();
}
const _easycom_up_swiper = () => "../../node-modules/uview-plus/components/u-swiper/u-swiper.js";
if (!Math) {
  (_easycom_up_swiper + CustomTabBar)();
}
const CustomTabBar = () => "../../components/custom-tab-bar/index.js";
const _sfc_main = {
  __name: "home",
  setup(__props) {
    common_vendor.onShow(() => {
      initMap();
      initAdvertData();
      initWarehouseData();
    });
    common_vendor.onReady(() => {
      initializePanelPositions();
    });
    const advertConfigList = common_vendor.ref([]);
    const currentType = common_vendor.ref("parking");
    const scale = common_vendor.ref(16);
    const center = common_vendor.ref({ latitude: 30.88, longitude: 121.81 });
    const markers = common_vendor.ref([]);
    const wareHouseList = common_vendor.ref([]);
    const chargingStations = common_vendor.ref([
      {
        id: "charging_001",
        name: "奕家江山路A区",
        latitude: 30.87,
        longitude: 121.81,
        type: "charging"
      }
    ]);
    const initAdvertData = async () => {
      try {
        const res = await api_advertConifg.getAdvertConfigList();
        advertConfigList.value = res.data.map((item) => ({
          url: item.picUrl
        }));
      } catch (error) {
        common_vendor.index.showToast({
          title: "广告数据加载失败",
          icon: "none"
        });
      }
    };
    const initWarehouseData = async () => {
      try {
        const res = await api_warehouse.getParkWareHouseList();
        wareHouseList.value = res.data.map((item) => ({
          id: item.id,
          name: item.warehouseName,
          latitude: item.latitude,
          longitude: item.longitude
        }));
        updateMarkers();
      } catch (error) {
        common_vendor.index.showToast({
          title: "仓库数据加载失败",
          icon: "none"
        });
      }
    };
    const switchToParking = () => {
      currentType.value = "parking";
      updateMarkers();
    };
    const switchToCharging = () => {
      currentType.value = "charging";
      updateMarkers();
    };
    const initMap = () => {
      try {
        checkLocationPermission();
      } catch (error) {
        common_vendor.index.showToast({
          title: "地图初始化失败",
          icon: "none"
        });
      }
    };
    const checkLocationPermission = async () => {
      try {
        const res = await common_vendor.index.getSetting({});
        if (!res.authSetting["scope.userLocation"]) {
          common_vendor.index.authorize({
            scope: "scope.userLocation",
            success: () => getCurrentLocation(),
            fail: () => {
              common_vendor.index.showModal({
                title: "定位权限未开启",
                content: "请在设置中开启定位权限以获取地图服务",
                confirmText: "去设置",
                success: (modalRes) => {
                  if (modalRes.confirm) {
                    common_vendor.index.openSetting({
                      success: (settingRes) => {
                        if (settingRes.authSetting["scope.userLocation"]) {
                          common_vendor.index.showToast({
                            title: "正在获取位置...",
                            icon: "loading",
                            duration: 1500
                          });
                          getCurrentLocation();
                        }
                      }
                    });
                  }
                }
              });
            }
          });
        } else {
          getCurrentLocation();
        }
      } catch (error) {
        common_vendor.index.showToast({
          title: "定位权限检查失败",
          icon: "none"
        });
      }
    };
    const getCurrentLocation = (showToast = false) => {
      common_vendor.index.getLocation({
        type: "gcj02",
        isHighAccuracy: true,
        success: (res) => {
          center.value = {
            latitude: res.latitude,
            longitude: res.longitude
          };
          common_vendor.index.setStorageSync("currentLocation", center.value);
          updateMarkers();
          if (showToast) {
            common_vendor.index.showToast({
              title: "定位成功",
              icon: "success",
              duration: 1500
            });
          }
        },
        fail: (error) => {
          console.error("获取位置失败:", error);
          common_vendor.index.showToast({
            title: "定位失败，请检查定位权限",
            icon: "none"
          });
        }
      });
    };
    const updateMarkers = () => {
      const markersList = [];
      if (currentType.value === "parking") {
        wareHouseList.value.forEach((warehouse, index) => {
          if (warehouse.latitude && warehouse.longitude) {
            markersList.push({
              id: index,
              // 使用索引作为id
              latitude: warehouse.latitude,
              longitude: warehouse.longitude,
              iconPath: "/static/image/parking-location.png",
              // 使用停车场图标
              width: 30,
              height: 30,
              callout: {
                content: warehouse.name,
                display: "ALWAYS",
                color: "#000000",
                bgColor: "#ffffff",
                fontSize: 14,
                fontWeight: "bold",
                padding: 5,
                borderRadius: 12
              }
            });
          }
        });
      } else if (currentType.value === "charging") {
        chargingStations.value.forEach((station, index) => {
          if (station.latitude && station.longitude) {
            markersList.push({
              id: index,
              // 使用索引作为id
              latitude: station.latitude,
              longitude: station.longitude,
              iconPath: "/static/image/charging-location.png",
              // 使用充电图标
              width: 30,
              height: 30,
              callout: {
                content: station.name,
                display: "ALWAYS",
                color: "#000000",
                bgColor: "#ffffff",
                fontSize: 14,
                fontWeight: "bold",
                padding: 5,
                borderRadius: 12
              }
            });
          }
        });
      }
      markers.value = markersList;
    };
    const overlayClasses = common_vendor.computed(() => ({
      "overlay-dragging": isDragging.value
    }));
    const overlayStyles = common_vendor.computed(() => ({
      transform: `translateY(${panelPosition.value}px)`,
      transition: isDragging.value ? "none" : "transform 0.3s cubic-bezier(0.4, 0, 0.2, 1)",
      height: `${panelHeight.value}px`,
      paddingBottom: `${tabBarHeight.value}px`
      // 内部为底部导航栏留出空间
    }));
    const controlsStyles = common_vendor.computed(() => {
      const controlsBottom = panelHeight.value + tabBarHeight.value + 10 - panelPosition.value;
      return {
        bottom: `${controlsBottom}px`
      };
    });
    const panelPosition = common_vendor.ref(0);
    const isDragging = common_vendor.ref(false);
    const startY = common_vendor.ref(0);
    const startPosition = common_vendor.ref(0);
    const isMinimized = common_vendor.ref(false);
    const tabBarHeight = common_vendor.ref(50);
    const PANEL_POSITIONS = {
      EXPANDED: 0,
      // 完整显示按钮和广告（初始状态）
      MINIMIZED: 0
      // 只显示按钮（下拉状态）
    };
    const screenHeight = common_vendor.ref(0);
    const panelHeight = common_vendor.ref(0);
    const initializePanelPositions = () => {
      const systemInfo = common_vendor.index.getSystemInfoSync();
      screenHeight.value = systemInfo.windowHeight;
      panelHeight.value = Math.round(screenHeight.value * 0.38);
      const advertHeight = 160;
      PANEL_POSITIONS.EXPANDED = 0;
      PANEL_POSITIONS.MINIMIZED = advertHeight;
      panelPosition.value = PANEL_POSITIONS.EXPANDED;
      isMinimized.value = false;
      const query = common_vendor.index.createSelectorQuery();
      query.select(".custom-tab-bar").boundingClientRect((data) => {
        if (data) {
          tabBarHeight.value = data.height;
        }
      }).exec();
    };
    const handleTouchStart = (e) => {
      if (!e.touches || !e.touches[0]) {
        return;
      }
      e.preventDefault();
      e.stopPropagation();
      console.log("拖拽开始", {
        EXPANDED: PANEL_POSITIONS.EXPANDED,
        MINIMIZED: PANEL_POSITIONS.MINIMIZED,
        currentPosition: panelPosition.value
      });
      isDragging.value = true;
      startY.value = e.touches[0].clientY;
      startPosition.value = panelPosition.value;
    };
    const handleTouchMove = (e) => {
      if (!isDragging.value) {
        return;
      }
      e.preventDefault();
      e.stopPropagation();
      if (!e.touches || !e.touches[0]) {
        return;
      }
      const currentY = e.touches[0].clientY;
      const deltaY = currentY - startY.value;
      let newPosition = startPosition.value + deltaY;
      newPosition = Math.max(
        PANEL_POSITIONS.EXPANDED,
        Math.min(PANEL_POSITIONS.MINIMIZED, newPosition)
      );
      panelPosition.value = newPosition;
    };
    const handleTouchEnd = (e) => {
      e.preventDefault();
      e.stopPropagation();
      isDragging.value = false;
      const distanceToExpanded = Math.abs(panelPosition.value - PANEL_POSITIONS.EXPANDED);
      const distanceToMinimized = Math.abs(panelPosition.value - PANEL_POSITIONS.MINIMIZED);
      const totalDistance = Math.abs(PANEL_POSITIONS.MINIMIZED - PANEL_POSITIONS.EXPANDED);
      if (distanceToExpanded > totalDistance / 3 && distanceToMinimized > totalDistance / 3) {
        const isMovingDown = panelPosition.value > startPosition.value;
        panelPosition.value = isMovingDown ? PANEL_POSITIONS.MINIMIZED : PANEL_POSITIONS.EXPANDED;
        isMinimized.value = isMovingDown;
      } else {
        const shouldMinimize = distanceToMinimized < distanceToExpanded;
        panelPosition.value = shouldMinimize ? PANEL_POSITIONS.MINIMIZED : PANEL_POSITIONS.EXPANDED;
        isMinimized.value = shouldMinimize;
      }
    };
    const handleParkingPayment = () => {
      if (common_vendor.index.getStorageSync("token")) {
        common_vendor.index.navigateTo({
          url: "/pages/payPlateQuery/payPlateQuery"
        });
      } else {
        common_vendor.index.showModal({
          title: "提示",
          content: "请先登录",
          success: (res) => {
            if (res.confirm) {
              common_vendor.index.navigateTo({
                url: "/pages/login/login"
              });
            }
          }
        });
      }
    };
    const handleChargingCode = () => {
      common_vendor.index.showToast({
        title: "暂未开放",
        icon: "none",
        duration: 2e3
      });
    };
    const backToMyLocation = async () => {
      try {
        const res = await common_vendor.index.getSetting({});
        if (!res.authSetting["scope.userLocation"]) {
          checkLocationPermission();
          return;
        }
        const mapContext = common_vendor.index.createMapContext("myMap");
        mapContext.moveToLocation({
          success: () => {
            common_vendor.index.showToast({
              title: "已回到当前位置",
              icon: "success",
              duration: 1500
            });
          },
          fail: (error) => {
            console.error("回到位置失败:", error);
            common_vendor.index.showToast({
              title: "正在重新定位...",
              icon: "loading",
              duration: 1500
            });
            getCurrentLocation(true);
          }
        });
      } catch (error) {
        common_vendor.index.showToast({
          title: "正在重新定位...",
          icon: "loading",
          duration: 1500
        });
        getCurrentLocation(true);
      }
    };
    return (_ctx, _cache) => {
      return {
        a: center.value.latitude,
        b: center.value.longitude,
        c: scale.value,
        d: markers.value,
        e: currentType.value === "parking" ? 1 : "",
        f: common_vendor.o(switchToParking),
        g: currentType.value === "charging" ? 1 : "",
        h: common_vendor.o(switchToCharging),
        i: common_vendor.s(controlsStyles.value),
        j: common_assets._imports_0,
        k: common_vendor.s(controlsStyles.value),
        l: common_vendor.o(backToMyLocation),
        m: common_vendor.o(handleTouchStart),
        n: common_vendor.o(handleTouchMove),
        o: common_vendor.o(handleTouchEnd),
        p: common_vendor.o(handleTouchEnd),
        q: common_assets._imports_1,
        r: common_vendor.o(handleParkingPayment),
        s: common_assets._imports_2,
        t: common_vendor.o(handleChargingCode),
        v: common_vendor.p({
          list: advertConfigList.value,
          autoplay: true,
          circular: true,
          interval: 3e3,
          duration: 500,
          height: "140"
        }),
        w: common_vendor.n(isMinimized.value ? "content-hidden" : ""),
        x: common_vendor.n(isMinimized.value ? "content-hidden" : ""),
        y: common_vendor.n(overlayClasses.value),
        z: common_vendor.s(overlayStyles.value)
      };
    };
  }
};
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-07e72d3c"]]);
wx.createPage(MiniProgramPage);
