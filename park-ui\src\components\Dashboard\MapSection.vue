<template>
  <!-- 地图展示区 -->
  <div class="map-section">
    <div class="map-container">
      <!-- 坐标校准测试组件 - 测试完成后注释 -->
      <!-- <CoordinateCalibration /> -->

      <!-- 高德地图容器 -->
      <div id="amap-container" ref="mapContainer"></div>



      <!-- 地图加载状态 -->
      <div v-if="mapLoading" class="map-loading">
        <div class="loading-spinner"></div>
        <div class="loading-text">地图加载中...</div>
      </div>
    </div>
  </div>
</template>

<script setup>
import AMapLoader from '@amap/amap-jsapi-loader'
import { ref, onMounted, onUnmounted, watch, provide } from 'vue'
import { getWarehouseLocations } from '@/api/dashboard/index'
import gcoord from 'gcoord'
// import CoordinateCalibration from './CoordinateCalibration.vue' // 测试完成后注释

// 定义emits
const emit = defineEmits(['update-statistics'])

// 地图相关数据
const mapContainer = ref()
const mapLoading = ref(true)
const warehouseLocations = ref([])
const coordinateMode = ref('customCorrection') // 坐标转换模式：默认使用实测校正
const mouseCoordinates = ref('移动鼠标查看坐标') // 鼠标实时坐标
const calibrationMode = ref(false) // 坐标校准模式
let amap = null
let markers = []
let AMap = null

// 初始化高德地图
const initAMap = async () => {
  try {
    mapLoading.value = true

    // 加载高德地图
    AMap = await AMapLoader.load({
      key: import.meta.env.VITE_AMAP_JS_KEY,
      version: '2.0',
      plugins: ['AMap.Scale', 'AMap.ToolBar', 'AMap.Geolocation']
    })

    // 创建地图实例
    amap = new AMap.Map(mapContainer.value, {
      zoom: 12, // 调整缩放级别以显示约10公里范围
      center: [121.813950, 30.884634], // 指定的中心点
      mapStyle: 'amap://styles/normal',
      showLabel: true
    })

    // 添加地图控件
    amap.addControl(new AMap.Scale())
    amap.addControl(new AMap.ToolBar({
      position: {
        top: '10px',
        right: '10px'
      }
    }))

    // 添加定位控件（不显示光圈）
    const geolocation = new AMap.Geolocation({
      enableHighAccuracy: true,
      timeout: 10000,
      maximumAge: 0,
      convert: true,
      showButton: true,
      buttonPosition: 'LB',
      buttonOffset: new AMap.Pixel(10, 20),
      showMarker: false, // 不显示定位标记
      showCircle: false, // 不显示精度圆圈
      panToLocation: false, // 不自动平移到定位位置
      zoomToAccuracy: false // 不自动调整缩放级别
    })

    amap.addControl(geolocation)

    // 添加鼠标移动事件监听器，实时显示坐标
    amap.on('mousemove', (e) => {
      const lng = e.lnglat.lng.toFixed(6)
      const lat = e.lnglat.lat.toFixed(6)
      mouseCoordinates.value = `${lng}, ${lat}`
    })

    // 添加地图点击事件，用于坐标校准
    amap.on('click', (e) => {
      if (calibrationMode.value) {
        const lng = e.lnglat.lng.toFixed(6)
        const lat = e.lnglat.lat.toFixed(6)
        console.log('点击位置坐标:', lng, lat)

        // 显示坐标校准信息
        const infoWindow = new AMap.InfoWindow({
          content: `
            <div style="padding: 12px;">
              <h4 style="margin: 0 0 8px 0; color: #2B6CB0;">坐标校准</h4>
              <p style="margin: 0 0 6px 0; font-size: 13px;">
                点击坐标：${lng}, ${lat}
              </p>
              <p style="margin: 0; font-size: 12px; color: #666;">
                请将此坐标与高德地图左下角显示的坐标对比
              </p>
            </div>
          `,
          position: e.lnglat
        })
        infoWindow.open(amap)
      }
    })

    // 获取场库位置数据并初始化标记
    await loadWarehouseLocations()
    updateMarkers()

    mapLoading.value = false

  } catch (error) {
    console.error('地图初始化失败:', error)
    mapLoading.value = false
  }
}

// 获取场库位置数据
const loadWarehouseLocations = async () => {
  try {
    const response = await getWarehouseLocations()
    if (response.code === 200 && response.data) {
      console.log('原始场库数据:', response.data)
      warehouseLocations.value = response.data.map(item => {
        const originalLat = parseFloat(item.latitude)
        const originalLng = parseFloat(item.longitude)

        // 根据选择的模式进行坐标转换
        let convertedCoords = [originalLng, originalLat]
        let coordinateSystem = '原始坐标'

        try {
          switch (coordinateMode.value) {
            case 'wgs84ToGcj02':
              convertedCoords = gcoord.transform(
                [originalLng, originalLat],
                gcoord.WGS84,
                gcoord.GCJ02
              )
              coordinateSystem = 'WGS84→GCJ02'
              break

            case 'gcj02ToWgs84':
              convertedCoords = gcoord.transform(
                [originalLng, originalLat],
                gcoord.GCJ02,
                gcoord.WGS84
              )
              coordinateSystem = 'GCJ02→WGS84'
              break

            case 'customCorrection':
              // 基于著雨苑实测数据的校正
              // 实测坐标：121.812009, 30.888840
              // 数据库坐标：121.818437, 30.89496
              // 偏移量：经度-0.006428，纬度-0.00612
              convertedCoords = [
                originalLng - 0.006428,  // 经度校正
                originalLat - 0.00612    // 纬度校正
              ]
              coordinateSystem = '实测校正(著雨苑基准)'
              break

            case 'original':
            default:
              convertedCoords = [originalLng, originalLat]
              coordinateSystem = '原始坐标'
              break
          }
        } catch (error) {
          console.warn('坐标转换失败，使用原始坐标:', error)
          convertedCoords = [originalLng, originalLat]
          coordinateSystem = '转换失败'
        }

        return {
          id: item.id,
          parkingNo: item.parkingNo,
          name: item.warehouseName,
          latitude: convertedCoords[1],  // 纬度
          longitude: convertedCoords[0], // 经度
          address: item.address,
          originalLatitude: originalLat,   // 保留原始坐标用于调试
          originalLongitude: originalLng,
          coordinateSystem: coordinateSystem // 坐标系转换信息
        }
      })
      console.log('转换后场库数据:', warehouseLocations.value)
    }
  } catch (error) {
    console.error('获取场库位置数据失败:', error)
  }
}

// 更新地图标记
const updateMarkers = () => {
  if (!amap) return

  // 清除现有标记
  if (markers.length > 0) {
    amap.remove(markers)
    markers = []
  }

  // 只显示停车场库
  let dataSource = warehouseLocations.value

  // 添加新标记
  dataSource.forEach(item => {
    if (item.latitude && item.longitude) {
      // 使用停车场库图标
      const iconUrl = new URL('@/assets/images/parking-marker.png', import.meta.url).href

      const marker = new AMap.Marker({
        position: [item.longitude, item.latitude],
        title: item.name,
        icon: new AMap.Icon({
          size: new AMap.Size(32, 32),
          image: iconUrl,
          imageSize: new AMap.Size(32, 32)
        }),
        // 添加标签显示场库名称（类似UniApp的callout）
        label: {
          content: item.name,
          offset: new AMap.Pixel(0, -4), // 标签位置偏移，非常靠近图标
          direction: 'top'
        }
      })

      // 设置标签样式
      marker.setLabel({
        content: `<div class="marker-label">${item.name}</div>`,
        offset: new AMap.Pixel(0, -4), // 调整偏移量，让标签非常靠近图标
        direction: 'top'
      })

      // 添加信息窗体（点击时显示详细信息）
      const infoWindow = new AMap.InfoWindow({
        content: `
          <div style="padding: 12px; min-width: 250px;">
            <h4 style="margin: 0 0 8px 0; color: #2B6CB0; font-size: 16px; font-weight: 600;">
              ${item.name}
            </h4>
            ${item.parkingNo ? `<p style="margin: 0 0 6px 0; color: #666; font-size: 13px; line-height: 1.4;">
              🏷️ 编号：${item.parkingNo}
            </p>` : ''}
            <p style="margin: 0 0 6px 0; color: #666; font-size: 13px; line-height: 1.4;">
              📍 ${item.address || '暂无地址信息'}
            </p>
            <p style="margin: 0 0 6px 0; color: #999; font-size: 12px;">
              🅿️ 停车场库
            </p>
            <details style="margin-top: 8px;">
              <summary style="color: #999; font-size: 11px; cursor: pointer;">坐标信息</summary>
              <div style="margin-top: 4px; font-size: 11px; color: #999;">
                <div>显示: ${item.latitude.toFixed(6)}, ${item.longitude.toFixed(6)}</div>
                <div>原始: ${item.originalLatitude.toFixed(6)}, ${item.originalLongitude.toFixed(6)}</div>
                <div>转换: ${item.coordinateSystem}</div>
              </div>
            </details>
          </div>
        `,
        offset: new AMap.Pixel(0, -35)
      })

      marker.on('click', () => {
        infoWindow.open(amap, marker.getPosition())
      })

      markers.push(marker)
    }
  })

  // 添加标记到地图
  if (markers.length > 0) {
    amap.add(markers)

    // 如果有多个标记，自适应显示所有标记
    if (markers.length > 1) {
      amap.setFitView(markers, false, [50, 50, 50, 50])
    } else if (markers.length === 1) {
      // 如果只有一个标记，以该标记为中心，保持当前缩放级别
      amap.setCenter(markers[0].getPosition())
    }
  }
}

// 切换坐标转换模式
const switchCoordinateMode = async (mode) => {
  coordinateMode.value = mode
  console.log('切换坐标转换模式:', mode)

  // 重新加载场库数据并更新地图标记
  await loadWarehouseLocations()
  updateMarkers()
}

// 启用坐标校准模式
const enableCalibration = () => {
  calibrationMode.value = !calibrationMode.value
  if (calibrationMode.value) {
    console.log('坐标校准模式已启用，点击地图任意位置查看坐标')
    alert('坐标校准模式已启用！\n\n使用方法：\n1. 点击地图上的任意位置\n2. 查看弹出的坐标信息\n3. 对比高德地图左下角显示的坐标\n4. 确定坐标是否一致')
  } else {
    console.log('坐标校准模式已关闭')
  }
}

// 定位到著雨苑
const locateZhuyuyuan = () => {
  const zhuyuyuan = warehouseLocations.value.find(item => item.name === '著雨苑')
  if (zhuyuyuan && amap) {
    // 设置地图中心点到著雨苑
    amap.setCenter([zhuyuyuan.longitude, zhuyuyuan.latitude])
    amap.setZoom(18) // 设置较大的缩放级别以便观察

    // 显示著雨苑的详细坐标信息
    const infoWindow = new AMap.InfoWindow({
      content: `
        <div style="padding: 15px; min-width: 300px;">
          <h4 style="margin: 0 0 10px 0; color: #2B6CB0;">著雨苑坐标分析</h4>
          <div style="font-size: 13px; line-height: 1.6;">
            <p><strong>数据库原始坐标：</strong><br>
            ${zhuyuyuan.originalLongitude}, ${zhuyuyuan.originalLatitude}</p>

            <p><strong>当前显示坐标：</strong><br>
            ${zhuyuyuan.longitude}, ${zhuyuyuan.latitude}</p>

            <p><strong>转换模式：</strong> ${zhuyuyuan.coordinateSystem}</p>

            <p style="color: #e74c3c;"><strong>请对比：</strong><br>
            查看高德地图左下角显示的坐标是否与上述坐标一致</p>
          </div>
        </div>
      `,
      position: [zhuyuyuan.longitude, zhuyuyuan.latitude]
    })
    infoWindow.open(amap)

    console.log('著雨苑坐标信息:', {
      原始坐标: `${zhuyuyuan.originalLongitude}, ${zhuyuyuan.originalLatitude}`,
      显示坐标: `${zhuyuyuan.longitude}, ${zhuyuyuan.latitude}`,
      转换模式: zhuyuyuan.coordinateSystem
    })
  } else {
    alert('未找到著雨苑数据，请确保数据已加载')
  }
}

// 坐标偏移分析
const analyzeCoordinateOffset = () => {
  // 著雨苑的实测数据
  const realCoords = { lng: 121.812009, lat: 30.888840 }  // 高德地图实际坐标
  const dbCoords = { lng: 121.818437, lat: 30.89496 }     // 数据库坐标

  // 计算偏移量
  const lngOffset = dbCoords.lng - realCoords.lng
  const latOffset = dbCoords.lat - realCoords.lat

  // 计算距离偏移（粗略计算）
  const lngDistance = lngOffset * 111000 * Math.cos(realCoords.lat * Math.PI / 180) // 经度1度约111km，需要乘以纬度余弦值
  const latDistance = latOffset * 111000 // 纬度1度约111km
  const totalDistance = Math.sqrt(lngDistance * lngDistance + latDistance * latDistance)

  console.log('坐标偏移分析:', {
    实际坐标: realCoords,
    数据库坐标: dbCoords,
    经度偏移: lngOffset.toFixed(6),
    纬度偏移: latOffset.toFixed(6),
    距离偏移: `${totalDistance.toFixed(0)}米`
  })

  // 显示分析结果
  const analysisWindow = new AMap.InfoWindow({
    content: `
      <div style="padding: 15px; min-width: 350px;">
        <h4 style="margin: 0 0 12px 0; color: #2B6CB0;">著雨苑坐标偏移分析</h4>

        <div style="background: #f8f9fa; padding: 10px; border-radius: 6px; margin-bottom: 10px;">
          <h5 style="margin: 0 0 6px 0; color: #495057;">实测数据对比</h5>
          <div style="font-size: 12px; line-height: 1.6;">
            <div><strong>高德地图实际位置：</strong> ${realCoords.lng}, ${realCoords.lat}</div>
            <div><strong>数据库存储坐标：</strong> ${dbCoords.lng}, ${dbCoords.lat}</div>
          </div>
        </div>

        <div style="background: #fff3cd; padding: 10px; border-radius: 6px; margin-bottom: 10px;">
          <h5 style="margin: 0 0 6px 0; color: #856404;">偏移量计算</h5>
          <div style="font-size: 12px; line-height: 1.6;">
            <div><strong>经度偏移：</strong> ${lngOffset.toFixed(6)}度 (${lngDistance.toFixed(0)}米)</div>
            <div><strong>纬度偏移：</strong> ${latOffset.toFixed(6)}度 (${latDistance.toFixed(0)}米)</div>
            <div><strong>总偏移距离：</strong> ${totalDistance.toFixed(0)}米</div>
          </div>
        </div>

        <div style="background: #d1ecf1; padding: 10px; border-radius: 6px;">
          <h5 style="margin: 0 0 6px 0; color: #0c5460;">校正建议</h5>
          <div style="font-size: 12px; line-height: 1.6;">
            <div>• 使用"实测校正"模式可获得最准确的位置</div>
            <div>• 校正公式：经度-0.006428，纬度-0.00612</div>
            <div>• 适用于所有使用相同坐标系的场库</div>
          </div>
        </div>
      </div>
    `,
    position: [realCoords.lng, realCoords.lat]
  })

  if (amap) {
    // 定位到著雨苑实际位置
    amap.setCenter([realCoords.lng, realCoords.lat])
    amap.setZoom(18)
    analysisWindow.open(amap)
  }
}

// 向子组件提供数据和方法 - 测试完成后注释
// provide('coordinateMode', coordinateMode)
// provide('mouseCoordinates', mouseCoordinates)
// provide('calibrationMode', calibrationMode)
// provide('switchCoordinateMode', switchCoordinateMode)
// provide('enableCalibration', enableCalibration)
// provide('locateZhuyuyuan', locateZhuyuyuan)
// provide('analyzeCoordinateOffset', analyzeCoordinateOffset)

// 监听场库位置数据变化
watch(() => warehouseLocations.value, () => {
  updateMarkers()
}, { deep: true })

onMounted(() => {
  initAMap()
})

onUnmounted(() => {
  if (amap) {
    amap.destroy()
  }
})
</script>

<style scoped>
/* 地图展示区 */
.map-section {
  flex: 0 0 70%;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

/* 星空主题地图区域 - 只在暗黑模式下生效 */
html.dark .map-section {
  background: rgba(255, 255, 255, 0.95) !important;
  border-radius: 16px !important;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3) !important;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.map-container {
  width: 100%;
  height: 100%;
  position: relative;
}

/* 移除了坐标转换控件样式，已迁移到CoordinateCalibration组件 */

#amap-container {
  width: 100%;
  height: 100%;
}





/* 地图加载状态 */
.map-loading {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.9);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 2000;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #2B6CB0;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 12px;
}

.loading-text {
  color: #666;
  font-size: 14px;
  font-weight: 500;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
</style>

<style>
/* 高德地图标签容器样式 */
.amap-marker-label {
  border: none !important;
  background: transparent !important;
  box-shadow: none !important;
}

/* 地图标记标签样式 */
.marker-label {
  background: #ffffff !important;
  color: #333333 !important;
  font-size: 12px !important;
  font-weight: 500 !important;
  padding: 4px 8px !important;
  border-radius: 6px !important;
  border: none !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
  white-space: nowrap !important;
  text-align: center !important;
  line-height: 1.2 !important;
  min-width: 50px !important;
  max-width: 100px !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
  position: relative !important;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
}

/* 标签悬停效果 */
.marker-label:hover {
  background: #f0f8ff !important;
  box-shadow: 0 3px 12px rgba(0, 0, 0, 0.15) !important;
  transform: translateY(-1px) !important;
  transition: all 0.2s ease !important;
}

/* 标签箭头（指向标记点） */
.marker-label::after {
  content: '' !important;
  position: absolute !important;
  bottom: -3px !important;
  left: 50% !important;
  transform: translateX(-50%) !important;
  width: 0 !important;
  height: 0 !important;
  border-left: 3px solid transparent !important;
  border-right: 3px solid transparent !important;
  border-top: 3px solid #ffffff !important;
}
</style>
