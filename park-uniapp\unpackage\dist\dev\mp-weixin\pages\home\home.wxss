/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* uni.scss */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.map-container.data-v-07e72d3c {
  height: 100vh;
}
.page-content.data-v-07e72d3c {
  margin-top: 88rpx;
  padding: 32rpx;
  padding-bottom: 140rpx;
}
.floating-overlay.data-v-07e72d3c {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  background: #ffffff;
  border-top-left-radius: 32rpx;
  border-top-right-radius: 32rpx;
  box-shadow: 0 -4rpx 16rpx rgba(0, 0, 0, 0.1);
  z-index: 100;
  will-change: transform;
  transform: translateZ(0);
  touch-action: none;
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -webkit-overflow-scrolling: touch;
}
.floating-overlay.overlay-dragging.data-v-07e72d3c {
  transition: none !important;
}
.drag-handle-container.data-v-07e72d3c {
  height: 50rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  touch-action: none;
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  cursor: grab;
}
.drag-handle-container.data-v-07e72d3c:active {
  cursor: grabbing;
}
.drag-handle-container .drag-handle-bar.data-v-07e72d3c {
  width: 60rpx;
  height: 6rpx;
  background: #cfcfcf;
  border-radius: 3rpx;
  pointer-events: none;
}
.buttons-container.data-v-07e72d3c {
  padding: 5rpx 30rpx 25rpx;
  display: flex;
  justify-content: space-around;
  align-items: center;
  gap: 24rpx;
  position: relative;
  z-index: 1;
}
.buttons-container .action-button.data-v-07e72d3c {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 20rpx 16rpx;
  border-radius: 24rpx;
  box-shadow: 0 4rpx 6rpx rgba(0, 0, 0, 0.08);
  border: 1rpx solid #f0f0f0;
}
.buttons-container .action-button .button-text.data-v-07e72d3c {
  margin-top: 12rpx;
  font-size: 26rpx;
  font-weight: 500;
  color: #000000;
}
.overlay-content.data-v-07e72d3c {
  padding: 0 30rpx;
  position: relative;
  z-index: 0;
}
.overlay-content .banner-section.data-v-07e72d3c {
  border-radius: 24rpx;
  transition: opacity 0.3s ease;
}
.overlay-content .banner-section.content-hidden.data-v-07e72d3c {
  opacity: 0;
  pointer-events: none;
}
.map-controls.data-v-07e72d3c {
  position: absolute;
  left: 20rpx;
  display: flex;
  flex-direction: column;
  gap: 16rpx;
  z-index: 10;
}
.map-controls .control-button.data-v-07e72d3c {
  width: 80rpx;
  height: 80rpx;
  border-radius: 14rpx;
  background-color: #ffffff;
  box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}
.map-controls .control-button.active.data-v-07e72d3c {
  background: linear-gradient(135deg, #4BA1FC, #777efe);
  color: #ffffff;
}
.map-controls .control-button.active .button-text.data-v-07e72d3c {
  color: #ffffff;
}
.map-controls .control-button.data-v-07e72d3c:active {
  transform: scale(0.95);
}
.map-controls .control-button .button-text-container.data-v-07e72d3c {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  gap: 2rpx;
}
.map-controls .control-button .button-text.data-v-07e72d3c {
  font-size: 24rpx;
  font-weight: 500;
  color: #333;
  text-align: center;
  line-height: 1.2;
}
.location-control.data-v-07e72d3c {
  position: absolute;
  right: 20rpx;
  width: 80rpx;
  height: 80rpx;
  background-color: #ffffff;
  border-radius: 14rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.1);
  transition: transform 0.2s ease, background-color 0.2s ease;
  z-index: 10;
}
.location-control.data-v-07e72d3c:active {
  transform: scale(0.95);
  background-color: #f5f5f5;
}