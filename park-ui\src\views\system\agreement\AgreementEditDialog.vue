<template>
  <!-- 协议编辑对话框 - 左右分栏布局 -->
  <el-dialog
    v-model="dialogVisible"
    width="90%"
    top="2vh"
    append-to-body
    destroy-on-close
    class="mobile-edit-dialog"
    :close-on-click-modal="false"
    :show-close="true"
    @update:model-value="handleDialogVisibleChange"
  >
    <template #header>
      <div class="dialog-header">
        <span>{{ isEdit ? '编辑协议' : '新增协议' }}</span>
      </div>
    </template>

    <!-- 左右分栏布局 -->
    <div class="edit-layout">
      <!-- 左侧：手机预览区域 -->
      <div class="left-panel">
        <div class="preview-title">预览效果</div>
        <!-- 使用独立的手机预览组件 -->
        <MobilePreview :agreement-content="form.agreementContent" />
      </div>

      <!-- 右侧：编辑区域 -->
      <div class="right-panel">
        <!-- 基本信息表单 -->
        <div class="form-section">
          <h3>基本信息</h3>
          <el-form ref="agreementRef" :model="form" :rules="rules" label-width="80px">
            <el-form-item label="协议类型" prop="agreementType">
              <el-input-number
                v-model="form.agreementType"
                placeholder="请输入协议类型"
                :min="0"
                controls-position="right"
                style="width: 100%"
              />
            </el-form-item>
            <el-form-item label="协议标题" prop="agreementTitle">
              <el-input v-model="form.agreementTitle" placeholder="请输入协议标题" />
            </el-form-item>
          </el-form>
        </div>

        <!-- AI助手区域 -->
        <div class="ai-assistant-section">
          <h3>AI助手</h3>
          <div class="ai-prompt-area">
            <div class="prompt-header">
              <span>复制以下提示词给AI，让AI帮您生成富文本内容：</span>
              <el-button size="small" @click="copyPrompt">复制提示词</el-button>
            </div>
            <el-input
              v-model="aiPrompt"
              type="textarea"
              :rows="8"
              readonly
              class="prompt-textarea"
            />
          </div>

          <div class="ai-result-area">
            <div class="result-header">
              <span>将AI生成的富文本内容粘贴到下方：</span>
              <div class="button-group">
                <el-button size="small" @click="copyPreviewContent">复制预览内容</el-button>
                <el-button size="small" @click="restoreOriginalContent">复原内容</el-button>
                <el-button size="small" @click="applyAiResult">应用到协议内容</el-button>
              </div>
            </div>
            <el-input
              v-model="aiResult"
              type="textarea"
              :rows="10"
              placeholder="请将AI生成的富文本内容粘贴到这里..."
              class="result-textarea"
            />
          </div>
        </div>

        <!-- 富文本编辑器 -->
        <div class="editor-section">
          <h3>协议内容编辑</h3>
          <div class="editor-wrapper">
            <editor
              v-model="form.agreementContent"
              :min-height="300"
              :height="null"
              :options="editorOptions"
              style="height: 100%; overflow: visible;"
            />
          </div>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleSave">保存协议</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup name="AgreementEditDialog">
import { computed, ref, nextTick, watch, getCurrentInstance } from 'vue'
import { ElMessage } from 'element-plus'
import MobilePreview from './MobilePreview.vue'

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  agreementData: {
    type: Object,
    default: () => ({})
  },
  isEdit: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:visible', 'save'])

// 获取当前实例
const { proxy } = getCurrentInstance();

// 控制弹窗显示状态
const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

// 表单数据
const form = ref({
  id: null,
  agreementType: null,
  agreementTitle: null,
  agreementContent: null
})

// 存储原始协议内容（用于复原功能）
const originalAgreementContent = ref('')

// AI助手相关数据
const aiResult = ref('')
const aiPrompt = ref(`请帮我生成一份协议的富文本内容，要求如下：

## 格式要求（重要！必须严格遵守）：
1. **HTML格式**：使用标准HTML标签，包含：
   - &lt;p style="margin-top:0;margin-right:0;margin-bottom:10px;margin-left:0;text-indent:0;padding:0 0 0 0;text-autospace:ideograph-numeric"&gt;标签包围每个段落
   - &lt;strong&gt;&lt;span style="font-family: Helvetica;letter-spacing: 0;font-size: 14px"&gt;标签标记重要标题和条款
   - 使用&lt;span style="font-family: Helvetica;letter-spacing: 0;font-size: 14px"&gt;包围正文内容
   - 使用&lt;span style="font-family: 宋体;letter-spacing: 0;font-size: 14px"&gt;包围中文标点符号

2. **样式规范**（必须完全按照以下格式）：
   - 主标题：&lt;p style="text-align:center"&gt;&lt;strong&gt;&lt;span style="font-family: Helvetica;letter-spacing: 0;font-size: 19px"&gt;协议标题&lt;/span&gt;&lt;/strong&gt;&lt;/p&gt;
   - 日期信息：&lt;p style="text-align:right"&gt;&lt;span style="font-family: Helvetica;letter-spacing: 0;font-size: 14px"&gt;更新日期: 2025年08月01日&lt;/span&gt;&lt;/p&gt;
   - 条款标题：&lt;strong&gt;&lt;span style="font-family: Helvetica;letter-spacing: 0;font-size: 14px"&gt;一、定义&lt;/span&gt;&lt;/strong&gt;
   - 正文段落：&lt;p style="margin-top:0;margin-right:0;margin-bottom:10px;margin-left:0;text-indent:0;padding:0 0 0 0;text-autospace:ideograph-numeric"&gt;&lt;span style="font-family: Helvetica;letter-spacing: 0;font-size: 14px"&gt;正文内容&lt;/span&gt;&lt;/p&gt;

## 内容结构要求（基于现有协议模板）：
1. **协议头部**：
   - 协议标题（居中，19px字体）
   - 更新日期：2025年08月01日（右对齐）
   - 生效日期：2025年08月01日（右对齐）
   - 欢迎语：欢迎您使用"临港捷运停车充电"微信小程序服务！
   - 服务提供方说明：本服务由"临港捷运停车充电"微信小程序开发者"临港捷运"（以下简称"开发者"）提供。

2. **必须包含的条款**（严格按照现有格式）：
   - 一、定义（用户、账户等概念定义）
   - 二、联系方式（客服邮箱：<EMAIL>，15个工作日内回复）
   - 三、服务内容（智慧停车服务，包括停车导航、停车缴费、停车预约等）
   - 四、注册账号与车牌绑定（微信手机号注册，车牌绑定流程）
   - 五、账户安全（用户责任，验证码保管）
   - 六、用户声明与保证（民事行为能力，真实信息，合规使用）
   - 七、服务的终止（终止条件，包括违规、联系方式失效等）
   - 八、服务的变更、中断（维护更新权利）
   - 九、服务条款修改（修改权利和通知方式）
   - 十、免责声明（数据来源说明，停车费用责任）
   - 十一、知识产权（【临港捷运】作为开发者的专有权利）
   - 十二、其他（法律适用，管辖法院）

## 主体信息规范：
- **服务名称**：临港捷运停车充电（用于微信小程序名称）
- **开发者名称**：临港捷运（用于公司身份）
- **知识产权主体**：【临港捷运】（用方括号标注）
- **联系邮箱**：<EMAIL>
- **服务范围**：智慧停车服务（停车导航、停车缴费、停车预约等）
- **日期信息**：2025年08月01日

## 语言风格要求：
- 使用正式、专业的法律文档语言
- 保持与现有协议一致的表述方式
- 条款编号使用中文数字（一、二、三...）
- 子条款使用阿拉伯数字（1、2、3...）
- 详细条款使用括号编号（（1）、（2）...）

## 特殊格式要求：
- 重要提醒内容使用红色字体：&lt;span style="color: rgb(255, 0, 0);"&gt;
- 邮箱地址使用红色标注：&lt;span style="color: rgb(255, 0, 0);"&gt;<EMAIL>&lt;/span&gt;
- 知识产权部分使用方括号：【临港捷运】
- 引用服务名称时使用双引号："临港捷运停车充电"

## 输出格式示例：
&lt;p style="text-align:center"&gt;
    &lt;strong&gt;&lt;span style="font-family: Helvetica;letter-spacing: 0;font-size: 19px"&gt;用户服务协议&lt;/span&gt;&lt;/strong&gt;
&lt;/p&gt;
&lt;p style="text-align:right"&gt;
    &lt;span style="font-family: Helvetica;letter-spacing: 0;font-size: 14px"&gt;更新日期: 2025年08月01日&lt;/span&gt;
&lt;/p&gt;
&lt;p style="margin-top:0;margin-right:0;margin-bottom:10px;margin-left:0;text-indent:0;padding:0 0 0 0;text-autospace:ideograph-numeric"&gt;
    &lt;span style="font-family: Helvetica;letter-spacing: 0;font-size: 14px"&gt;欢迎您使用&lt;/span&gt;&lt;span style="font-family: 宋体;letter-spacing: 0;font-size: 14px"&gt;"&lt;/span&gt;&lt;span style="font-family: Helvetica;letter-spacing: 0;font-size: 14px"&gt;临港捷运停车充电&lt;/span&gt;&lt;span style="font-family: 宋体;letter-spacing: 0;font-size: 14px"&gt;"&lt;/span&gt;&lt;span style="font-family: Helvetica;letter-spacing: 0;font-size: 14px"&gt;微信小程序服务！&lt;/span&gt;
&lt;/p&gt;

请根据我提供的具体需求内容，生成完整的富文本协议内容：

我的具体需求：
[请在这里详细描述您的协议需求，包括：
- 协议类型（用户服务协议/隐私政策/其他）
- 特殊服务功能或条款
- 特定的免责要求
- 其他特殊要求]`)

// 表单验证规则
const rules = ref({
  agreementType: [
    { required: true, message: "协议类型不能为空", trigger: "change" }
  ],
  agreementTitle: [
    { required: true, message: "协议标题不能为空", trigger: "blur" }
  ],
  agreementContent: [
    { required: true, message: "协议内容不能为空", trigger: "blur" }
  ]
})

// 协议类型改为数字输入

// 富文本编辑器配置
const editorOptions = ref({
  placeholder: '请输入协议内容...',
  scroll: true,
  maxLength: 50000,
  MENU_CONF: {
    fontSize: {
      fontSizeList: ['12px', '14px', '16px', '18px', '20px', '24px', '28px', '32px']
    },
    fontFamily: {
      fontFamilyList: [
        '黑体', '仿宋', '楷体', '标楷体', '华文仿宋', '华文楷体', '宋体',
        'Arial', 'Tahoma', 'Verdana'
      ]
    }
  }
})

// 表单引用
const agreementRef = ref(null)

// 监听协议数据变化，更新表单
watch(() => props.agreementData, (newData) => {
  if (newData && Object.keys(newData).length > 0) {
    form.value = { ...newData }
    // 保存原始协议内容
    originalAgreementContent.value = newData.agreementContent || ''
  } else {
    resetForm()
  }
}, { immediate: true, deep: true })

// 重置表单
function resetForm() {
  form.value = {
    id: null,
    agreementType: null,
    agreementTitle: null,
    agreementContent: null
  }
  originalAgreementContent.value = ''
}

// 复制AI提示词
function copyPrompt() {
  navigator.clipboard.writeText(aiPrompt.value).then(() => {
    ElMessage.success('提示词已复制到剪贴板')
  }).catch(() => {
    ElMessage.error('复制失败，请手动复制')
  })
}

// 应用AI生成的结果
function applyAiResult() {
  if (aiResult.value.trim()) {
    form.value.agreementContent = aiResult.value
    ElMessage.success('AI生成的内容已应用到协议内容')
  } else {
    ElMessage.warning('请先粘贴AI生成的内容')
  }
}

// 格式化协议内容（增强版本，更好地保留对齐格式）
function formatAgreementContent(content) {
  if (!content) return '';

  // 保留格式的清理，特别注意保持文本对齐
  let formatted = content
    // 移除完全空的p标签
    .replace(/<p[^>]*>\s*<\/p>/gi, '')
    // 移除只包含&nbsp;的p标签
    .replace(/<p[^>]*>(\s|&nbsp;)*<\/p>/gi, '')
    // 保留并强化文本对齐样式（优先处理，避免被覆盖）
    .replace(/text-align:\s*center/gi, 'text-align: center !important')
    .replace(/text-align:\s*left/gi, 'text-align: left !important')
    .replace(/text-align:\s*right/gi, 'text-align: right !important')
    // 给p标签添加适当的样式，但保留原有的style属性
    .replace(/<p([^>]*?)>/gi, (match, attributes) => {
      // 如果已经有style属性，在其中添加margin和line-height
      if (attributes.includes('style=')) {
        return match.replace(/style="([^"]*)"/, (styleMatch, styleContent) => {
          // 保留原有样式，添加新的样式
          let newStyle = styleContent;
          if (!styleContent.includes('margin')) {
            newStyle += '; margin: 8px 0';
          }
          if (!styleContent.includes('line-height')) {
            newStyle += '; line-height: 1.6';
          }
          return `style="${newStyle}"`;
        });
      } else {
        // 如果没有style属性，添加新的style属性
        return `<p${attributes} style="margin: 8px 0; line-height: 1.6;">`;
      }
    })
    // 保留强调标签
    .replace(/<strong([^>]*)>/gi, '<strong$1 style="font-weight: bold;">')
    // 保留span标签的样式，特别是字体和对齐相关的
    .replace(/<span([^>]*?)>/gi, (match, attributes) => {
      // 确保span标签的样式被保留
      return match;
    })
    // 减少过多的&nbsp;但保留一些空格（更保守的处理）
    .replace(/&nbsp;{6,}/g, '&nbsp;&nbsp;&nbsp;&nbsp;')
    // 清理过多的连续空白，但保留必要的空格
    .replace(/\s{4,}/g, '  ');

  return formatted;
}

// 复制手机预览中的富文本内容
function copyPreviewContent() {
  if (!form.value.agreementContent) {
    ElMessage.warning('当前没有协议内容可复制')
    return
  }

  // 获取格式化后的内容（与手机预览中显示的一致）
  // 这里我们直接复制当前编辑器中的内容，因为它已经包含了所有的格式信息
  const contentToCopy = form.value.agreementContent

  navigator.clipboard.writeText(contentToCopy).then(() => {
    ElMessage.success('当前编辑器内容已复制到剪贴板')
  }).catch(() => {
    ElMessage.error('复制失败，请手动复制')
  })
}

// 复原到数据库中的原始内容
function restoreOriginalContent() {
  if (!originalAgreementContent.value) {
    ElMessage.warning('没有原始内容可复原')
    return
  }

  form.value.agreementContent = originalAgreementContent.value
  ElMessage.success('已复原到数据库中的原始内容')
}

// 格式化方法已移至MobilePreview组件中

// 取消操作
function handleCancel() {
  emit('update:visible', false)
}

// 保存操作
function handleSave() {
  agreementRef.value?.validate((valid) => {
    if (valid) {
      emit('save', form.value)
    }
  })
}

// 对话框状态变化处理
function handleDialogVisibleChange(value) {
  emit('update:visible', value)
  if (!value) {
    resetForm()
  }
}

// 修复编辑器滚动问题
function fixEditorScroll() {
  setTimeout(() => {
    const editorWrapper = document.querySelector('.mobile-edit-dialog .editor-wrapper');
    if (editorWrapper) {
      const editor = editorWrapper.querySelector('.editor');
      if (editor) {
        editor.style.height = '100%';
        editor.style.display = 'flex';
        editor.style.flexDirection = 'column';
      }

      const quillContainer = editorWrapper.querySelector('.ql-container');
      if (quillContainer) {
        quillContainer.style.flex = '1';
        quillContainer.style.overflowY = 'auto';
        quillContainer.style.height = 'auto';
        quillContainer.style.maxHeight = 'none';
      }

      const qlEditor = editorWrapper.querySelector('.ql-editor');
      if (qlEditor) {
        qlEditor.style.overflowY = 'auto';
        qlEditor.style.maxHeight = 'none';
        qlEditor.style.height = 'auto';
        qlEditor.style.minHeight = '200px';
      }

      const toolbar = editorWrapper.querySelector('.ql-toolbar');
      if (toolbar) {
        toolbar.style.position = 'sticky';
        toolbar.style.top = '0';
        toolbar.style.zIndex = '100';
        toolbar.style.backgroundColor = '#fff';
        toolbar.style.flexShrink = '0';
      }
    }
  }, 500);
}

// 监听弹窗打开状态，修复编辑器滚动
watch(dialogVisible, (newVal) => {
  if (newVal) {
    nextTick(() => {
      fixEditorScroll();
      setTimeout(() => fixEditorScroll(), 1000);
      setTimeout(() => fixEditorScroll(), 2000);
    });
  }
});
</script>

<style scoped>
/* 按钮组样式 */
.button-group {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.button-group .el-button {
  margin: 0;
}

/* 确保按钮在小屏幕上也能正常显示 */
@media (max-width: 768px) {
  .button-group {
    flex-direction: column;
    gap: 4px;
  }

  .button-group .el-button {
    width: 100%;
  }
}
</style>

<style>
/* 编辑弹窗样式 */
.mobile-edit-dialog .el-dialog {
  max-width: 1400px;
  height: 85vh;
  max-height: 85vh;
}

.mobile-edit-dialog .el-dialog__body {
  padding: 0 !important;
  height: calc(85vh - 120px);
  overflow: hidden;
}

.dialog-header {
  font-size: 18px;
  font-weight: 600;
  color: #303133;
}

/* 左右分栏布局 */
.edit-layout {
  display: flex;
  height: 100%;
  gap: 20px;
}

.left-panel {
  flex: 0 0 320px;
  display: flex;
  flex-direction: column;
}

.right-panel {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.preview-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 12px;
  text-align: center;
}

/* 手机预览样式已移至MobilePreview组件中 */

/* 右侧面板样式 */
.form-section, .ai-assistant-section, .editor-section {
  margin-bottom: 20px;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  padding: 16px;
  background: #fff;
}

.form-section h3, .ai-assistant-section h3, .editor-section h3 {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  border-bottom: 1px solid #e4e7ed;
  padding-bottom: 8px;
}

/* AI助手区域样式 */
.ai-prompt-area, .ai-result-area {
  margin-bottom: 16px;
}

.prompt-header, .result-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  font-size: 14px;
  color: #606266;
}

.prompt-textarea, .result-textarea {
  font-family: 'Courier New', monospace;
  font-size: 12px;
}

.prompt-textarea {
  background-color: #f5f7fa;
}

/* 编辑器区域样式 */
.editor-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.editor-wrapper {
  flex: 1;
  overflow: hidden;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  background: #fff;
}

/* 手机预览相关样式已移至MobilePreview组件中 */

/* 手机状态栏和导航栏样式已移至MobilePreview组件中 */

/* 内容区域样式 */
.mobile-edit-dialog .mobile-content-area {
  flex: 1;
  overflow-y: auto;
  background: #fff;
  height: calc(100% - 56px);
  display: flex;
  flex-direction: column;
}

/* 协议表单样式 */
.mobile-edit-dialog .agreement-form {
  padding: 12px;
  border-bottom: 1px solid #f0f0f0;
  flex-shrink: 0;
}

.mobile-edit-dialog .agreement-form .el-form-item {
  margin-bottom: 12px;
}

.mobile-edit-dialog .agreement-form .el-form-item__label {
  font-size: 12px;
  line-height: 1.4;
}

.mobile-edit-dialog .agreement-form .el-input__inner,
.mobile-edit-dialog .agreement-form .el-select {
  font-size: 12px;
}

/* 协议编辑器样式 */
.mobile-edit-dialog .agreement-editor {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: 12px;
  overflow: hidden;
}

.mobile-edit-dialog .editor-label {
  font-size: 12px;
  color: #606266;
  line-height: 1.4;
  margin-bottom: 8px;
  font-weight: 600;
  flex-shrink: 0;
}

.mobile-edit-dialog .editor-wrapper {
  flex: 1;
  overflow: hidden;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  background: #fff;
}

/* 滚动条样式 */
.mobile-edit-dialog .mobile-content-area::-webkit-scrollbar {
  width: 4px;
}

.mobile-edit-dialog .mobile-content-area::-webkit-scrollbar-track {
  background: transparent;
}

.mobile-edit-dialog .mobile-content-area::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 2px;
}

.mobile-edit-dialog .mobile-content-area::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.3);
}

/* 响应式适配 */
@media (max-width: 1200px) {
  .edit-layout {
    flex-direction: column;
  }

  .left-panel {
    flex: 0 0 auto;
    order: 2;
  }

  .right-panel {
    flex: 1;
    order: 1;
  }

  /* 手机框架响应式样式在MobilePreview组件中处理 */
}

@media (max-width: 768px) {
  .mobile-edit-dialog .el-dialog {
    width: 95% !important;
    margin: 0 auto;
  }

  .edit-layout {
    gap: 10px;
  }

  /* 手机框架响应式样式在MobilePreview组件中处理 */

  .form-section, .ai-assistant-section, .editor-section {
    padding: 12px;
    margin-bottom: 12px;
  }
}
</style>
