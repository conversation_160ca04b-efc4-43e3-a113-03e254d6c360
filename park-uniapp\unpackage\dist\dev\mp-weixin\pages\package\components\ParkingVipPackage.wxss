/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* uni.scss */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.parking-vip-package.data-v-b8425ce1 {
  padding: 0 20rpx;
  min-height: 100vh;
}
.vip-package-list .vip-card.data-v-b8425ce1 {
  display: flex;
  flex-direction: column;
  border-radius: 16rpx;
  overflow: hidden;
  background-image: url("https://test-parknew.lgfw24hours.com:3443/statics/wx/specialVipHeader.png");
  background-size: 100% 100%;
  background-repeat: no-repeat;
  padding-bottom: 40rpx;
}
.vip-package-list .vip-card .content-section.data-v-b8425ce1 {
  color: #fff;
  display: flex;
  align-items: center;
  padding: 20rpx 20rpx 0 20rpx;
}
.vip-package-list .vip-card .content-section .vip-info.data-v-b8425ce1 {
  padding: 10rpx 30rpx 0;
  display: flex;
  flex-direction: column;
  justify-content: center;
}
.vip-package-list .vip-card .content-section .vip-info .vip-title-container.data-v-b8425ce1 {
  margin: 5rpx 0;
  font-size: 32rpx;
}
.vip-package-list .vip-card .content-section .vip-info .vip-plate.data-v-b8425ce1 {
  font-size: 36rpx;
  font-weight: bold;
  margin: 10rpx 0;
}
.vip-package-list .vip-card .content-section .vip-info .vip-expire.data-v-b8425ce1 {
  font-size: 24rpx;
}
.vip-package-list .vip-card .content-section .right-section.data-v-b8425ce1 {
  margin-left: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.vip-package-list .vip-card .content-section .right-section .car-image.data-v-b8425ce1 {
  width: 180rpx;
  height: 180rpx;
}
.vip-package-list .vip-card .content-section .right-section .car-image image.data-v-b8425ce1 {
  width: 100%;
  height: 100%;
}
.vip-package-list .vip-card .button-section.data-v-b8425ce1 {
  display: flex;
  justify-content: flex-end;
  margin-right: 40rpx;
}
.vip-package-list .vip-card .button-section .vip-link.data-v-b8425ce1 {
  background: #007aff;
  padding: 5rpx 20rpx;
  border-radius: 40rpx;
  text-align: center;
  font-size: 28rpx;
  font-weight: bold;
  color: #fff;
}
.vip-package-list .vip-card .button-section .vip-link.disabled.data-v-b8425ce1 {
  background: #ccc;
  color: #999;
}
.notice-section.data-v-b8425ce1 {
  margin-top: 30rpx;
  padding: 20rpx;
  background: #fff;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.06);
}
.notice-section .notice-title.data-v-b8425ce1 {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 16rpx;
  padding-bottom: 12rpx;
  border-bottom: 1rpx solid #f0f0f0;
}
.notice-section .notice-content .notice-text.data-v-b8425ce1 {
  font-size: 26rpx;
  color: #666;
  line-height: 1.6;
  display: block;
}