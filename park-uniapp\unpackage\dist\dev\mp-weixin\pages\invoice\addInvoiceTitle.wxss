/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* uni.scss */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.invoice-form-container.data-v-6a82be67 {
  min-height: 100vh;
  background-color: #f8f9fa;
  padding: 32rpx;
}
.form-content.data-v-6a82be67 {
  background: #ffffff;
  border-radius: 24rpx;
  padding: 48rpx 36rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.06);
}
.form-row.data-v-6a82be67 {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 40rpx;
}
.form-row.data-v-6a82be67:last-child {
  margin-bottom: 0;
}
.form-row .form-label.data-v-6a82be67 {
  font-size: 30rpx;
  color: #1f2937;
  margin-right: 10rpx;
}
.form-row .form-options.data-v-6a82be67 {
  display: flex;
  gap: 16rpx;
  flex: 1;
  margin-left: 20rpx;
}
.form-row .form-options .option-item.data-v-6a82be67 {
  flex: 1;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2rpx solid #e5e7eb;
  border-radius: 12rpx;
  background: #ffffff;
  transition: all 0.3s ease;
}
.form-row .form-options .option-item.active.data-v-6a82be67 {
  border-color: #4BA1FC;
  background: #f0f9ff;
}
.form-row .form-options .option-item.active .option-text.data-v-6a82be67 {
  color: #4BA1FC;
  font-weight: 500;
}
.form-row .form-options .option-item.data-v-6a82be67:active {
  transform: scale(0.98);
}
.form-row .form-options .option-item .option-text.data-v-6a82be67 {
  font-size: 26rpx;
  color: #6b7280;
  transition: all 0.3s ease;
}
.form-row .form-input.data-v-6a82be67 {
  flex: 1;
  margin-left: 20rpx;
}
.form-row .form-input .input-field.data-v-6a82be67 {
  width: 100%;
  height: 50rpx;
  padding: 0 8rpx 5rpx 8rpx;
  border: none;
  border-bottom: 2rpx solid #e5e7eb;
  background: transparent;
  font-size: 28rpx;
  color: #1f2937;
  transition: all 0.3s ease;
}
.form-row .form-input .input-field.data-v-6a82be67:focus {
  border-bottom-color: #4BA1FC;
  outline: none;
}
.form-row .form-input .input-field.data-v-6a82be67::-webkit-input-placeholder {
  color: #9ca3af;
}
.form-row .form-input .input-field.data-v-6a82be67::placeholder {
  color: #9ca3af;
}
.save-button.data-v-6a82be67 {
  margin-top: 40rpx;
  background: linear-gradient(90deg, #4BA1FC 0%, #7e6dff 100%);
  border-radius: 16rpx;
  padding: 20rpx 0;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 16rpx rgba(75, 161, 252, 0.25);
  transition: all 0.3s ease;
}
.save-button.data-v-6a82be67:active {
  transform: translateY(1rpx);
  box-shadow: 0 2rpx 8rpx rgba(75, 161, 252, 0.2);
}
.save-button .save-text.data-v-6a82be67 {
  font-size: 30rpx;
  font-weight: 500;
  color: #ffffff;
  letter-spacing: 1rpx;
}
.form-row.data-v-6a82be67 {
  transition: all 0.4s ease;
}
.form-row[data-v-enter-active].data-v-6a82be67, .form-row[data-v-leave-active].data-v-6a82be67 {
  transition: all 0.4s ease;
}
.form-row[data-v-enter-from].data-v-6a82be67, .form-row[data-v-leave-to].data-v-6a82be67 {
  opacity: 0;
  transform: translateY(-20rpx);
}