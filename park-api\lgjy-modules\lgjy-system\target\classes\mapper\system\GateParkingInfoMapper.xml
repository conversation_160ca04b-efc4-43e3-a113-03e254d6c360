<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lgjy.system.mapper.GateParkingInfoMapper">
    
    <resultMap type="GateParkingInfo" id="GateParkingInfoResult">
        <result property="id"    column="id"    />
        <result property="parkingId"    column="parkingId"    />
        <result property="parkingName"    column="parking_name"    />
        <result property="plateNum"    column="plateNum"    />
        <result property="carType"    column="carType"    />
        <result property="inTime"    column="inTime"    />
        <result property="inDateTime"    column="in_date_time"    />
        <result property="inChannelId"    column="inChannelId"    />
        <result property="inChannelName"    column="inChannelName"    />
        <result property="inPic"    column="inPic"    />
        <result property="outTime"    column="outTime"    />
        <result property="outDateTime"    column="out_date_time"    />
        <result property="outChannelId"    column="outChannelId"    />
        <result property="outChannelName"    column="outChannelName"    />
        <result property="outPic"    column="outPic"    />
        <result property="money"    column="money"    />
        <result property="payType"    column="payType"    />
        <result property="status"    column="status"    />
        <result property="lastUpdate"    column="lastUpdate"    />
        <result property="parkingDuration"    column="parking_duration"    />
        <result property="parkingDurationText"    column="parking_duration_text"    />
    </resultMap>

    <sql id="selectGateParkingInfoVo">
        select 
            gpi.id, 
            gpi.parkingId, 
            COALESCE(mw.warehouse_name, gpi.parkingId) as parking_name,
            gpi.plateNum, 
            gpi.carType, 
            gpi.inTime, 
            CASE 
                WHEN gpi.inTime IS NOT NULL AND gpi.inTime != '' AND gpi.inTime != '0' 
                THEN FROM_UNIXTIME(CAST(gpi.inTime AS UNSIGNED))
                ELSE NULL 
            END as in_date_time,
            gpi.inChannelId, 
            gpi.inChannelName, 
            gpi.inPic, 
            gpi.outTime, 
            CASE 
                WHEN gpi.outTime IS NOT NULL AND gpi.outTime != '' AND gpi.outTime != '0' 
                THEN FROM_UNIXTIME(CAST(gpi.outTime AS UNSIGNED))
                ELSE NULL 
            END as out_date_time,
            gpi.outChannelId, 
            gpi.outChannelName, 
            gpi.outPic, 
            CAST(gpi.money AS DECIMAL(10,2)) as money,
            gpi.payType,
            gpi.status,
            gpi.lastUpdate,
            CASE 
                WHEN gpi.outTime IS NOT NULL AND gpi.outTime != '' AND gpi.outTime != '0' AND gpi.inTime IS NOT NULL AND gpi.inTime != '' AND gpi.inTime != '0'
                THEN ROUND((CAST(gpi.outTime AS UNSIGNED) - CAST(gpi.inTime AS UNSIGNED)) / 60)
                WHEN gpi.inTime IS NOT NULL AND gpi.inTime != '' AND gpi.inTime != '0'
                THEN ROUND((UNIX_TIMESTAMP(NOW()) - CAST(gpi.inTime AS UNSIGNED)) / 60)
                ELSE 0
            END as parking_duration,
            CASE 
                WHEN gpi.outTime IS NOT NULL AND gpi.outTime != '' AND gpi.outTime != '0' AND gpi.inTime IS NOT NULL AND gpi.inTime != '' AND gpi.inTime != '0'
                THEN CONCAT(
                    FLOOR((CAST(gpi.outTime AS UNSIGNED) - CAST(gpi.inTime AS UNSIGNED)) / 3600), '小时',
                    FLOOR(((CAST(gpi.outTime AS UNSIGNED) - CAST(gpi.inTime AS UNSIGNED)) % 3600) / 60), '分钟'
                )
                WHEN gpi.inTime IS NOT NULL AND gpi.inTime != '' AND gpi.inTime != '0'
                THEN CONCAT(
                    FLOOR((UNIX_TIMESTAMP(NOW()) - CAST(gpi.inTime AS UNSIGNED)) / 3600), '小时',
                    FLOOR(((UNIX_TIMESTAMP(NOW()) - CAST(gpi.inTime AS UNSIGNED)) % 3600) / 60), '分钟'
                )
                ELSE '0分钟'
            END as parking_duration_text
        from gate_parking_info gpi
        left join mini_warehouse mw on gpi.parkingId = mw.id
    </sql>

    <select id="selectGateParkingInfoList" parameterType="GateParkingInfo" resultMap="GateParkingInfoResult">
        <include refid="selectGateParkingInfoVo"/>
        <where>
            <if test="plateNum != null  and plateNum != ''"> and gpi.plateNum like concat('%', #{plateNum}, '%')</if>
            <if test="parkingId != null  and parkingId != ''"> and gpi.parkingId = #{parkingId}</if>
            <if test="parkingName != null  and parkingName != ''"> and mw.warehouse_name like concat('%', #{parkingName}, '%')</if>
            <if test="carType != null  and carType != ''"> and gpi.carType = #{carType}</if>
            <if test="inChannelName != null  and inChannelName != ''"> and gpi.inChannelName like concat('%', #{inChannelName}, '%')</if>
            <if test="outChannelName != null  and outChannelName != ''"> and gpi.outChannelName like concat('%', #{outChannelName}, '%')</if>
            <if test="payType != null  and payType != ''"> and gpi.payType = #{payType}</if>
            <if test="status != null"> and gpi.status = #{status}</if>
            <if test="inBeginTime != null and inBeginTime != ''"> and gpi.inTime >= UNIX_TIMESTAMP(#{inBeginTime})</if>
            <if test="inEndTime != null and inEndTime != ''"> and gpi.inTime &lt;= UNIX_TIMESTAMP(#{inEndTime})</if>
            <if test="outBeginTime != null and outBeginTime != ''"> and gpi.outTime >= UNIX_TIMESTAMP(#{outBeginTime}) and gpi.outTime != '' and gpi.outTime != '0'</if>
            <if test="outEndTime != null and outEndTime != ''"> and gpi.outTime &lt;= UNIX_TIMESTAMP(#{outEndTime}) and gpi.outTime != '' and gpi.outTime != '0'</if>
        </where>
        order by gpi.lastUpdate desc
    </select>
    
    <select id="selectGateParkingInfoById" parameterType="String" resultMap="GateParkingInfoResult">
        <include refid="selectGateParkingInfoVo"/>
        where gpi.id = #{id}
    </select>

    <select id="selectGateParkingInfoByPlateNum" parameterType="String" resultMap="GateParkingInfoResult">
        <include refid="selectGateParkingInfoVo"/>
        where gpi.plateNum like concat('%', #{plateNum}, '%')
        order by gpi.lastUpdate desc
    </select>

    <select id="selectGateParkingInfoByParkingId" parameterType="String" resultMap="GateParkingInfoResult">
        <include refid="selectGateParkingInfoVo"/>
        where gpi.parkingId = #{parkingId}
        order by gpi.lastUpdate desc
    </select>

    <select id="selectGateParkingInfoByStatus" parameterType="Integer" resultMap="GateParkingInfoResult">
        <include refid="selectGateParkingInfoVo"/>
        where gpi.status = #{status}
        order by gpi.lastUpdate desc
    </select>

    <select id="countGateParkingInfo" parameterType="GateParkingInfo" resultType="int">
        select count(*) from gate_parking_info gpi
        <where>  
            <if test="plateNum != null  and plateNum != ''"> and gpi.plateNum like concat('%', #{plateNum}, '%')</if>
            <if test="parkingId != null  and parkingId != ''"> and gpi.parkingId = #{parkingId}</if>
            <if test="carType != null  and carType != ''"> and gpi.carType = #{carType}</if>
            <if test="status != null"> and gpi.status = #{status}</if>
        </where>
    </select>

    <select id="selectCarTypeOptions" resultType="String">
        select distinct carType from gate_parking_info where carType is not null and carType != '' order by carType
    </select>

    <select id="selectPayTypeOptions" resultType="String">
        select distinct payType from gate_parking_info where payType is not null and payType != '' order by payType
    </select>

    <select id="selectChannelNameOptions" resultType="String">
        select distinct channel_name from (
            select inChannelName as channel_name from gate_parking_info where inChannelName is not null and inChannelName != ''
            union
            select outChannelName as channel_name from gate_parking_info where outChannelName is not null and outChannelName != ''
        ) t order by channel_name
    </select>
        
    <insert id="insertGateParkingInfo" parameterType="GateParkingInfo">
        insert into gate_parking_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="parkingId != null">parkingId,</if>
            <if test="plateNum != null">plateNum,</if>
            <if test="carType != null">carType,</if>
            <if test="inTime != null">inTime,</if>
            <if test="inChannelId != null">inChannelId,</if>
            <if test="inChannelName != null">inChannelName,</if>
            <if test="inPic != null">inPic,</if>
            <if test="outTime != null">outTime,</if>
            <if test="outChannelId != null">outChannelId,</if>
            <if test="outChannelName != null">outChannelName,</if>
            <if test="outPic != null">outPic,</if>
            <if test="money != null">money,</if>
            <if test="payType != null">payType,</if>
            <if test="status != null">status,</if>
            <if test="lastUpdate != null">lastUpdate,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="parkingId != null">#{parkingId},</if>
            <if test="plateNum != null">#{plateNum},</if>
            <if test="carType != null">#{carType},</if>
            <if test="inTime != null">#{inTime},</if>
            <if test="inChannelId != null">#{inChannelId},</if>
            <if test="inChannelName != null">#{inChannelName},</if>
            <if test="inPic != null">#{inPic},</if>
            <if test="outTime != null">#{outTime},</if>
            <if test="outChannelId != null">#{outChannelId},</if>
            <if test="outChannelName != null">#{outChannelName},</if>
            <if test="outPic != null">#{outPic},</if>
            <if test="money != null">#{money},</if>
            <if test="payType != null">#{payType},</if>
            <if test="status != null">#{status},</if>
            <if test="lastUpdate != null">#{lastUpdate},</if>
         </trim>
    </insert>

    <update id="updateGateParkingInfo" parameterType="GateParkingInfo">
        update gate_parking_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="parkingId != null">parkingId = #{parkingId},</if>
            <if test="plateNum != null">plateNum = #{plateNum},</if>
            <if test="carType != null">carType = #{carType},</if>
            <if test="inTime != null">inTime = #{inTime},</if>
            <if test="inChannelId != null">inChannelId = #{inChannelId},</if>
            <if test="inChannelName != null">inChannelName = #{inChannelName},</if>
            <if test="inPic != null">inPic = #{inPic},</if>
            <if test="outTime != null">outTime = #{outTime},</if>
            <if test="outChannelId != null">outChannelId = #{outChannelId},</if>
            <if test="outChannelName != null">outChannelName = #{outChannelName},</if>
            <if test="outPic != null">outPic = #{outPic},</if>
            <if test="money != null">money = #{money},</if>
            <if test="payType != null">payType = #{payType},</if>
            <if test="status != null">status = #{status},</if>
            <if test="lastUpdate != null">lastUpdate = #{lastUpdate},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteGateParkingInfoById" parameterType="String">
        delete from gate_parking_info where id = #{id}
    </delete>

    <delete id="deleteGateParkingInfoByIds" parameterType="String">
        delete from gate_parking_info where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
