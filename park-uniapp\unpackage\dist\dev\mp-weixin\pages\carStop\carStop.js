"use strict";
const common_vendor = require("../../common/vendor.js");
const common_assets = require("../../common/assets.js");
const api_login = require("../../api/login.js");
const api_parkingOrder = require("../../api/parkingOrder.js");
const _sfc_main = {
  __name: "carStop",
  setup(__props) {
    const gateNo = common_vendor.ref(null);
    const warehouse = common_vendor.ref(null);
    const openid = common_vendor.ref(null);
    common_vendor.onLoad((options) => {
      console.log("小程序接收到的参数:", options);
      let stopNoValue = null;
      if (options.stopNo) {
        stopNoValue = options.stopNo;
        console.log("直接传参模式，stopNo:", stopNoValue);
      } else if (options.q) {
        try {
          const decodedUrl = decodeURIComponent(options.q);
          console.log("解码后的URL:", decodedUrl);
          const queryString = decodedUrl.split("?")[1];
          if (queryString) {
            const params = queryString.split("&");
            for (let param of params) {
              const [key, value] = param.split("=");
              if (key === "stopNo") {
                stopNoValue = value;
                break;
              }
            }
          }
          console.log("扫码跳转模式，解析出的stopNo:", stopNoValue);
        } catch (error) {
          console.error("URL解析失败:", error);
          common_vendor.index.showToast({
            title: "URL解析失败",
            icon: "error"
          });
          return;
        }
      }
      if (stopNoValue) {
        let splitStr = stopNoValue.split("wId");
        if (splitStr.length === 2) {
          gateNo.value = splitStr[0];
          warehouse.value = splitStr[1];
          console.log("解析成功:", {
            gateNo: gateNo.value,
            warehouse: warehouse.value
          });
          createParkingOrder();
        } else {
          console.error("stopNo参数格式错误:", stopNoValue);
          common_vendor.index.showToast({
            title: "参数格式错误",
            icon: "error"
          });
        }
      } else {
        console.error("缺少stopNo参数");
        common_vendor.index.showToast({
          title: "缺少必要参数",
          icon: "error"
        });
      }
    });
    const createParkingOrder = () => {
      common_vendor.index.showLoading({
        title: "正在获取支付信息...",
        mask: true
      });
      common_vendor.index.login({
        provider: "weixin",
        success: (res) => {
          api_login.getOpenid({ wxCode: res.code }).then((openidRes) => {
            openid.value = openidRes.data;
            if (!openid.value) {
              common_vendor.index.hideLoading();
              common_vendor.index.showToast({
                title: "获取用户信息失败",
                icon: "error"
              });
              return;
            }
            initiatePayment();
          }).catch((error) => {
            console.error("获取openid失败:", error);
            common_vendor.index.hideLoading();
            common_vendor.index.showToast({
              title: "获取用户信息失败",
              icon: "error"
            });
          });
        },
        fail: (error) => {
          console.error("微信登录失败:", error);
          common_vendor.index.hideLoading();
          common_vendor.index.showToast({
            title: "微信登录失败",
            icon: "error"
          });
        }
      });
    };
    const initiatePayment = () => {
      const params = {
        openId: openid.value,
        gateNo: gateNo.value,
        warehouseId: warehouse.value
      };
      console.log("支付参数:", params);
      api_parkingOrder.paymentTemporary(params).then((res) => {
        common_vendor.index.hideLoading();
        if (!res.data) {
          common_vendor.index.showToast({
            title: "获取支付信息失败",
            icon: "error"
          });
          return;
        }
        common_vendor.index.requestPayment({
          timeStamp: res.data.timeStamp,
          nonceStr: res.data.nonceStr,
          package: res.data.package,
          signType: res.data.signType,
          paySign: res.data.paySign,
          success: function(result) {
            console.log("支付成功:", result);
            common_vendor.index.showToast({
              title: "支付成功",
              icon: "success",
              duration: 2e3
              // complete: function () {
              //     setTimeout(() => {
              //         uni.navigateBack();
              //     }, 1000);
              // }
            });
          },
          fail: function(err) {
            console.error("支付失败:", err);
            common_vendor.index.showToast({
              title: "支付失败",
              icon: "error"
            });
          }
        });
      }).catch((error) => {
        console.error("获取支付信息失败:", error);
        common_vendor.index.hideLoading();
        common_vendor.index.showToast({
          title: "获取支付信息失败",
          icon: "error"
        });
      });
    };
    return (_ctx, _cache) => {
      return {
        a: common_assets._imports_0$7
      };
    };
  }
};
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-50a9b85c"]]);
wx.createPage(MiniProgramPage);
