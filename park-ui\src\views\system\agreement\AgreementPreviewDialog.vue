<template>
  <!-- 协议详情对话框 - 手机端预览样式 -->
  <el-dialog
    v-model="dialogVisible"
    width="400px"
    top="2vh"
    append-to-body
    destroy-on-close
    class="mobile-preview-dialog"
    :close-on-click-modal="false"
    :show-close="true"
    @update:model-value="handleDialogVisibleChange"
  >
    <!-- 全屏手机框架 -->
    <div class="fullscreen-phone-frame">
        <!-- 手机端协议内容 -->
        <div class="mobile-agreement-content">
              <!-- 手机状态栏 -->
              <div class="mobile-status-bar">
                <span class="status-time"></span>
                <div class="status-right">
                  <span class="status-signal">●●●●</span>
                  <span class="status-wifi">📶</span>
                  <span class="status-battery">99% 🔋</span>
                </div>
              </div>

              <!-- 导航栏 -->
              <div class="mobile-nav-bar">
                <div class="nav-left">
                  <i class="nav-back">←</i>
                </div>
                <div class="nav-center">
                  <span class="nav-title">协议详情</span>
                </div>
                <div class="nav-right">
                  <i class="nav-more">⋯</i>
                  <i class="nav-share">⚪</i>
                </div>
              </div>

              <!-- 协议内容区域 -->
              <div class="mobile-content-area">


                <!-- 协议正文内容 -->
                <div class="agreement-text" v-html="formatAgreementContent(agreementData.agreementContent)"></div>
              </div>

              <!-- 底部安全区域 -->
              <div class="mobile-safe-area"></div>
            </div>
    </div>
  </el-dialog>
</template>

<script setup name="AgreementPreviewDialog">
import { computed } from 'vue'

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  agreementData: {
    type: Object,
    default: () => ({})
  }
})

const emit = defineEmits(['update:visible'])

// 控制弹窗显示状态
const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

/** 获取协议类型文本 */
function getAgreementTypeText(agreementType) {
  const typeMap = {
    1: '用户服务协议',
    2: '隐私政策',
    3: '发票抬头协议'
  };
  return typeMap[agreementType] || '协议详情';
}

/** 格式化协议内容 */
function formatAgreementContent(content) {
  if (!content) return '';

  // 保留格式的清理
  let formatted = content
    // 移除完全空的p标签
    .replace(/<p[^>]*>\s*<\/p>/gi, '')
    // 移除只包含&nbsp;的p标签
    .replace(/<p[^>]*>(\s|&nbsp;)*<\/p>/gi, '')
    // 给p标签添加适当的样式，保持段落间距
    .replace(/<p([^>]*)>/gi, '<p$1 style="margin: 8px 0; line-height: 1.6;">')
    // 保留文本对齐样式
    .replace(/text-align:\s*center/gi, 'text-align: center !important')
    .replace(/text-align:\s*left/gi, 'text-align: left !important')
    .replace(/text-align:\s*right/gi, 'text-align: right !important')
    // 减少过多的&nbsp;但保留一些空格
    .replace(/&nbsp;{4,}/g, '&nbsp;&nbsp;&nbsp;')
    // 保留强调标签
    .replace(/<strong([^>]*)>/gi, '<strong$1 style="font-weight: bold;">')
    // 清理过多的连续空白，但不要太激进
    .replace(/\s{3,}/g, ' ');

  return formatted;
}

// 时间格式化函数 - 需要从utils引入或者父组件传递
function parseTime(time, pattern) {
  if (!time) return '';
  
  const date = new Date(time);
  const o = {
    y: date.getFullYear(),
    m: date.getMonth() + 1,
    d: date.getDate(),
    h: date.getHours(),
    i: date.getMinutes(),
    s: date.getSeconds()
  };
  
  return pattern.replace(/{([ymdhis])+}/g, (result, key) => {
    let value = o[key];
    if (key === 'm' || key === 'd' || key === 'h' || key === 'i' || key === 's') {
      value = value.toString().padStart(2, '0');
    }
    return value;
  });
}

function handleClose() {
  emit('update:visible', false)
}

function handleDialogVisibleChange(value) {
  emit('update:visible', value)
}
</script>

<style>
/* 手机端预览弹窗样式 */
.mobile-preview-dialog .el-dialog {
  background: transparent;
  padding: 0;
  border-radius: 0;
  box-shadow: none;
  backdrop-filter: none;
  margin: 0;
}

.mobile-preview-dialog .el-dialog__header {
  position: absolute;
  top: 10px;
  right: -20px;
  z-index: 1000;
  padding: 0;
  background: none;
  border: none;
}

.mobile-preview-dialog .el-dialog__headerbtn {
  position: static;
  top: auto;
  right: auto;
  width: 32px;
  height: 32px;
  background: rgba(0, 0, 0, 0.8);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  border: 2px solid rgba(255, 255, 255, 0.4);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

.mobile-preview-dialog .el-dialog__headerbtn:hover {
  background: rgba(0, 0, 0, 0.95);
  transform: scale(1.05);
  border-color: rgba(255, 255, 255, 0.6);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.4);
}

.mobile-preview-dialog .el-dialog__close {
  color: #ffffff;
  font-size: 16px;
  font-weight: bold;
}

.mobile-preview-dialog .el-dialog__title {
  display: none;
}

.mobile-preview-dialog .el-dialog__body {
  padding: 0 !important;
  height: 100%;
}

/* 全屏手机框架 */
.fullscreen-phone-frame {
  width: 100%;
  height: 780px;
  background: #ffffff;
  border-radius: 25px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3), 0 0 0 1px rgba(255, 255, 255, 0.1);
  overflow: hidden;
  position: relative;
  border: 6px solid #1a1a1a;
  margin: 0;
}

/* 现代手机内容样式 */
.mobile-agreement-content {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  background: #ffffff;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
  overflow: hidden;
  border-radius: 19px;
}

/* 底部安全区域 */
.mobile-safe-area {
  height: 15px;
  background: #ffffff;
  flex-shrink: 0;
}

/* 手机状态栏样式 */
.mobile-status-bar {
  height: 20px;
  background: #000;
  color: #fff;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 12px;
  font-size: 10px;
  font-weight: 600;
}

.status-time {
  font-weight: 600;
}

.status-right {
  display: flex;
  align-items: center;
  gap: 3px;
}

.status-signal, .status-wifi, .status-battery {
  font-size: 8px;
}

/* 导航栏样式 */
.mobile-nav-bar {
  height: 36px;
  background: #f8f8f8;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 12px;
  border-bottom: 1px solid #e0e0e0;
  position: relative;
}

.nav-left, .nav-right {
  display: flex;
  align-items: center;
  gap: 6px;
  width: 50px;
}

.nav-right {
  justify-content: flex-end;
}

.nav-center {
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  align-items: center;
}

.nav-title {
  font-size: 14px;
  font-weight: 600;
  color: #333;
  max-width: 160px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.nav-back, .nav-more, .nav-share {
  font-size: 14px;
  color: #333;
  cursor: pointer;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 内容区域样式 */
.mobile-content-area {
  flex: 1;
  overflow-y: auto;
  background: #fff;
  height: calc(100% - 56px); /* 减去状态栏和导航栏的高度 */
}



/* 协议正文样式 */
.agreement-text {
  padding: 12px;
  line-height: 1.6;
  font-size: 12px;
  color: #333;
  word-wrap: break-word;
  overflow-wrap: break-word;
}

/* 协议文本内部元素样式 */
.agreement-text h1,
.agreement-text h2,
.agreement-text h3,
.agreement-text h4,
.agreement-text h5,
.agreement-text h6 {
  margin: 20px 0 12px 0;
  font-weight: 600;
  color: #333;
  line-height: 1.4;
}

.agreement-text h1 { font-size: 15px; }
.agreement-text h2 { font-size: 14px; }
.agreement-text h3 { font-size: 13px; }
.agreement-text h4 { font-size: 12px; }
.agreement-text h5 { font-size: 12px; }
.agreement-text h6 { font-size: 12px; }

.agreement-text p {
  margin: 8px 0;
  line-height: 1.6;
  text-align: justify;
}

.agreement-text ul,
.agreement-text ol {
  margin: 8px 0;
  padding-left: 16px;
}

.agreement-text li {
  margin: 6px 0;
  line-height: 1.5;
}

.agreement-text strong {
  font-weight: 600;
  color: #333;
}

.agreement-text em {
  font-style: italic;
  color: #666;
}

/* 滚动条样式 */
.mobile-content-area::-webkit-scrollbar {
  width: 4px;
}

.mobile-content-area::-webkit-scrollbar-track {
  background: transparent;
}

.mobile-content-area::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 2px;
}

.mobile-content-area::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.3);
}

/* 响应式适配 */
@media (max-width: 600px) {
  .mobile-preview-dialog .el-dialog {
    width: 95% !important;
    margin: 0 auto;
  }

  .fullscreen-phone-frame {
    height: 70vh;
    max-height: 600px;
  }
}

@media (max-height: 800px) {
  .fullscreen-phone-frame {
    height: 70vh;
  }
}
</style>