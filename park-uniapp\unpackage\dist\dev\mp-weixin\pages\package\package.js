"use strict";
const common_vendor = require("../../common/vendor.js");
const api_specialUser = require("../../api/specialUser.js");
if (!Math) {
  (ParkingNormalPackage + ParkingVipPackage + ChargingFastPackage + CustomTabBar)();
}
const CustomTabBar = () => "../../components/custom-tab-bar/index.js";
const ParkingNormalPackage = () => "./components/ParkingNormalPackage.js";
const ChargingFastPackage = () => "./components/ChargingFastPackage.js";
const ParkingVipPackage = () => "./components/ParkingVipPackage.js";
const _sfc_main = {
  __name: "package",
  setup(__props) {
    const PACKAGE_TYPES = {
      PARKING: "停车套餐",
      CHARGING: "充电套餐"
    };
    const SUB_TYPES = {
      NORMAL: "普通套餐",
      VIP: "VIP套餐",
      GROUP: "集团套餐",
      CHARGING_DEFAULT: "充电套餐"
      // 充电套餐默认子类型
    };
    const USER_TYPES = {
      VIP: "VIP客户",
      GROUP: "集团客户"
    };
    const USER_TYPE_CONFIG = {
      [USER_TYPES.VIP]: [
        { name: SUB_TYPES.VIP },
        { name: SUB_TYPES.NORMAL }
      ],
      [USER_TYPES.GROUP]: [
        { name: SUB_TYPES.GROUP },
        { name: SUB_TYPES.NORMAL }
      ],
      "default": [
        { name: SUB_TYPES.NORMAL }
      ]
    };
    const specialUser = common_vendor.ref({});
    const parkingSubTypes = common_vendor.ref([]);
    const chargingSubTypes = common_vendor.ref([]);
    const selectedPackageType = common_vendor.ref({ name: PACKAGE_TYPES.PARKING });
    const selectedSubType = common_vendor.ref({ name: SUB_TYPES.NORMAL });
    const componentKey = common_vendor.ref(0);
    const parkingNormalRef = common_vendor.ref(null);
    const parkingVipRef = common_vendor.ref(null);
    const chargingFastRef = common_vendor.ref(null);
    const getParkingSubTypesByUserType = (userType) => {
      return USER_TYPE_CONFIG[userType] || USER_TYPE_CONFIG["default"];
    };
    common_vendor.onShow(() => {
      api_specialUser.getSpecialUser().then((res) => {
        var _a;
        specialUser.value = res.data || {};
        const userType = (_a = res.data) == null ? void 0 : _a.userType;
        parkingSubTypes.value = getParkingSubTypesByUserType(userType);
        loadAndValidateSelectedTypes();
        forceComponentReload();
      });
    });
    const loadAndValidateSelectedTypes = () => {
      let needUpdate = false;
      try {
        const cachedPackageType = common_vendor.index.getStorageSync("selectedPackageType");
        const cachedSubType = common_vendor.index.getStorageSync("selectedSubType");
        if (cachedPackageType) {
          selectedPackageType.value = cachedPackageType;
        }
        if (cachedSubType) {
          selectedSubType.value = cachedSubType;
        }
        if (selectedPackageType.value.name === PACKAGE_TYPES.PARKING) {
          const currentSubTypeNames = parkingSubTypes.value.map((item) => item.name);
          if (!currentSubTypeNames.includes(selectedSubType.value.name)) {
            selectedSubType.value = parkingSubTypes.value[0];
            needUpdate = true;
          }
        } else if (selectedPackageType.value.name === PACKAGE_TYPES.CHARGING) {
          selectedSubType.value = { name: SUB_TYPES.CHARGING_DEFAULT };
        }
        if (needUpdate) {
          saveSelectedTypesToCache();
        }
      } catch (error) {
        console.log("读取缓存失败:", error);
        selectedPackageType.value = { name: PACKAGE_TYPES.PARKING };
        selectedSubType.value = parkingSubTypes.value[0] || { name: SUB_TYPES.NORMAL };
        saveSelectedTypesToCache();
      }
    };
    const saveSelectedTypesToCache = () => {
      try {
        common_vendor.index.setStorageSync("selectedPackageType", selectedPackageType.value);
        common_vendor.index.setStorageSync("selectedSubType", selectedSubType.value);
      } catch (error) {
        console.log("保存缓存失败:", error);
      }
    };
    const forceComponentReload = () => {
      componentKey.value++;
      common_vendor.nextTick$1(() => {
        const tryInitData = (retryCount = 0) => {
          const currentRef = getCurrentComponentRef();
          if (currentRef && currentRef.initData) {
            currentRef.initData();
          } else if (retryCount < 5) {
            setTimeout(() => {
              tryInitData(retryCount + 1);
            }, 200 * (retryCount + 1));
          }
        };
        setTimeout(() => {
          tryInitData();
        }, 150);
      });
    };
    const getCurrentComponentRef = () => {
      if (selectedPackageType.value.name === PACKAGE_TYPES.PARKING) {
        if (selectedSubType.value.name === SUB_TYPES.NORMAL) {
          return parkingNormalRef.value;
        } else if (selectedSubType.value.name === SUB_TYPES.VIP || selectedSubType.value.name === SUB_TYPES.GROUP) {
          return parkingVipRef.value;
        }
      } else if (selectedPackageType.value.name === PACKAGE_TYPES.CHARGING) {
        return chargingFastRef.value;
      }
      return null;
    };
    const currentSubTypes = common_vendor.computed(() => {
      return selectedPackageType.value.name === PACKAGE_TYPES.PARKING ? parkingSubTypes.value : chargingSubTypes.value;
    });
    const selectPackageType = (typeName) => {
      selectedPackageType.value = { name: typeName };
      if (typeName === PACKAGE_TYPES.PARKING) {
        selectedSubType.value = currentSubTypes.value[0];
      } else if (typeName === PACKAGE_TYPES.CHARGING) {
        selectedSubType.value = { name: SUB_TYPES.CHARGING_DEFAULT };
      }
      saveSelectedTypesToCache();
      forceComponentReload();
    };
    const selectSubType = (subType) => {
      if (selectedSubType.value.name === subType.name)
        return;
      selectedSubType.value = subType;
      saveSelectedTypesToCache();
      forceComponentReload();
    };
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_vendor.t(PACKAGE_TYPES.PARKING),
        b: selectedPackageType.value.name === PACKAGE_TYPES.PARKING ? 1 : "",
        c: common_vendor.o(($event) => selectPackageType(PACKAGE_TYPES.PARKING)),
        d: common_vendor.t(PACKAGE_TYPES.CHARGING),
        e: selectedPackageType.value.name === PACKAGE_TYPES.CHARGING ? 1 : "",
        f: common_vendor.o(($event) => selectPackageType(PACKAGE_TYPES.CHARGING)),
        g: currentSubTypes.value.length > 0
      }, currentSubTypes.value.length > 0 ? {
        h: common_vendor.f(currentSubTypes.value, (item, index, i0) => {
          return {
            a: common_vendor.t(item.name),
            b: index,
            c: common_vendor.n(selectedSubType.value.name === item.name ? "active" : ""),
            d: common_vendor.o(($event) => selectSubType(item), index)
          };
        })
      } : {}, {
        i: selectedPackageType.value.name === PACKAGE_TYPES.PARKING && selectedSubType.value.name === SUB_TYPES.NORMAL
      }, selectedPackageType.value.name === PACKAGE_TYPES.PARKING && selectedSubType.value.name === SUB_TYPES.NORMAL ? {
        j: common_vendor.sr(parkingNormalRef, "9a861780-0", {
          "k": "parkingNormalRef"
        }),
        k: componentKey.value
      } : {}, {
        l: selectedPackageType.value.name === PACKAGE_TYPES.PARKING && (selectedSubType.value.name === SUB_TYPES.VIP || selectedSubType.value.name === SUB_TYPES.GROUP)
      }, selectedPackageType.value.name === PACKAGE_TYPES.PARKING && (selectedSubType.value.name === SUB_TYPES.VIP || selectedSubType.value.name === SUB_TYPES.GROUP) ? {
        m: common_vendor.sr(parkingVipRef, "9a861780-1", {
          "k": "parkingVipRef"
        }),
        n: componentKey.value,
        o: common_vendor.p({
          userType: selectedSubType.value.name === SUB_TYPES.GROUP ? SUB_TYPES.GROUP : SUB_TYPES.VIP
        })
      } : {}, {
        p: selectedPackageType.value.name === PACKAGE_TYPES.CHARGING
      }, selectedPackageType.value.name === PACKAGE_TYPES.CHARGING ? {
        q: common_vendor.sr(chargingFastRef, "9a861780-2", {
          "k": "chargingFastRef"
        }),
        r: componentKey.value
      } : {});
    };
  }
};
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-9a861780"]]);
wx.createPage(MiniProgramPage);
