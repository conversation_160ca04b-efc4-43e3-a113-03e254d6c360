<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lgjy.system.mapper.MiniAdvertConfigMapper">

    <resultMap type="com.lgjy.system.domain.MiniAdvertConfig" id="MiniAdvertConfigResult">
        <result property="id"            column="id"            />
        <result property="remark"        column="remark"        />
        <result property="advertTitle"   column="advert_title"  />
        <result property="url"           column="url"           />
        <result property="picUrl"        column="pic_url"       />
        <result property="status"        column="status"        />
        <result property="deleteFlag"    column="delete_flag"   />
        <result property="createBy"      column="create_by"     />
        <result property="createTime"    column="create_time"   />
        <result property="updateBy"      column="update_by"     />
        <result property="updateTime"    column="update_time"   />
    </resultMap>

    <sql id="selectMiniAdvertConfigVo">
        select ac.id, ac.remark, ac.advert_title, ac.url, ac.pic_url, ac.status, ac.delete_flag,
               ac.create_by, ac.create_time, ac.update_by, ac.update_time
        from mini_advert_config ac
    </sql>

    <select id="selectMiniAdvertConfigList" parameterType="com.lgjy.system.domain.MiniAdvertConfig" resultMap="MiniAdvertConfigResult">
        <include refid="selectMiniAdvertConfigVo"/>
        <where>
            ac.delete_flag = 0
            <if test="advertTitle != null  and advertTitle != ''"> and ac.advert_title like concat('%', #{advertTitle}, '%')</if>
            <if test="url != null  and url != ''"> and ac.url = #{url}</if>
            <if test="picUrl != null  and picUrl != ''"> and ac.pic_url = #{picUrl}</if>
            <if test="status != null "> and ac.status = #{status}</if>
        </where>
        order by ac.create_time desc
    </select>

    <select id="selectMiniAdvertConfigById" parameterType="Long" resultMap="MiniAdvertConfigResult">
        <include refid="selectMiniAdvertConfigVo"/>
        where ac.id = #{id} and ac.delete_flag = 0
    </select>

    <select id="checkAdvertTitleUnique" parameterType="String" resultMap="MiniAdvertConfigResult">
        <include refid="selectMiniAdvertConfigVo"/>
        where ac.advert_title = #{advertTitle} and ac.delete_flag = 0 limit 1
    </select>

    <insert id="insertMiniAdvertConfig" parameterType="com.lgjy.system.domain.MiniAdvertConfig" useGeneratedKeys="true" keyProperty="id">
        insert into mini_advert_config
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="remark != null">remark,</if>
            <if test="advertTitle != null and advertTitle != ''">advert_title,</if>
            <if test="url != null">url,</if>
            <if test="picUrl != null">pic_url,</if>
            <if test="status != null">status,</if>
            <if test="deleteFlag != null">delete_flag,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="remark != null">#{remark},</if>
            <if test="advertTitle != null and advertTitle != ''">#{advertTitle},</if>
            <if test="url != null">#{url},</if>
            <if test="picUrl != null">#{picUrl},</if>
            <if test="status != null">#{status},</if>
            <if test="deleteFlag != null">#{deleteFlag},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateMiniAdvertConfig" parameterType="com.lgjy.system.domain.MiniAdvertConfig">
        update mini_advert_config
        <trim prefix="SET" suffixOverrides=",">
            <if test="remark != null">remark = #{remark},</if>
            <if test="advertTitle != null and advertTitle != ''">advert_title = #{advertTitle},</if>
            <if test="url != null">url = #{url},</if>
            <if test="picUrl != null">pic_url = #{picUrl},</if>
            <if test="status != null">status = #{status},</if>
            <if test="deleteFlag != null">delete_flag = #{deleteFlag},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteMiniAdvertConfigById" parameterType="Long">
        update mini_advert_config set delete_flag = 1 where id = #{id}
    </delete>

    <delete id="deleteMiniAdvertConfigByIds" parameterType="String">
        update mini_advert_config set delete_flag = 1 where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
