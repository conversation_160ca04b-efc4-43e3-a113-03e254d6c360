"use strict";
const common_vendor = require("../../common/vendor.js");
const common_assets = require("../../common/assets.js");
const utils_utils = require("../../utils/utils.js");
const api_login = require("../../api/login.js");
if (!Array) {
  const _easycom_up_icon2 = common_vendor.resolveComponent("up-icon");
  _easycom_up_icon2();
}
const _easycom_up_icon = () => "../../node-modules/uview-plus/components/u-icon/u-icon.js";
if (!Math) {
  _easycom_up_icon();
}
const _sfc_main = {
  __name: "login",
  setup(__props) {
    const menuButtonHeight = common_vendor.ref(0);
    const menuButtonTop = common_vendor.ref(0);
    const initSystemInfo = () => {
      try {
        const menuButtonInfo = common_vendor.index.getMenuButtonBoundingClientRect();
        menuButtonHeight.value = menuButtonInfo.height;
        menuButtonTop.value = menuButtonInfo.top;
      } catch (error) {
        console.error("获取系统信息失败:", error);
      }
    };
    const handleBack = () => {
      common_vendor.index.navigateBack({
        delta: 1,
        fail: () => {
          common_vendor.index.switchTab({
            url: "/pages/home/<USER>"
          });
        }
      });
    };
    const loginForm = common_vendor.ref({
      phoneNumber: "",
      msgCode: "",
      wxCode: ""
    });
    const buttonText = common_vendor.ref("获取验证码");
    const buttonDisabled = common_vendor.ref(false);
    let countdownInterval = null;
    const fetchCode = async () => {
      if (loginForm.value.phoneNumber === "" || loginForm.value.phoneNumber.length !== 11) {
        common_vendor.index.showToast({
          title: "请输入正确的手机号",
          icon: "none"
        });
        return;
      }
      buttonDisabled.value = true;
      let timeLeft = 60;
      buttonText.value = ` ${timeLeft} s`;
      countdownInterval = setInterval(() => {
        timeLeft--;
        if (timeLeft > 0) {
          buttonText.value = ` ${timeLeft} s`;
        } else {
          clearInterval(countdownInterval);
          buttonText.value = "获取验证码";
          buttonDisabled.value = false;
        }
      }, 1e3);
      await api_login.getCode({
        phoneNumber: loginForm.value.phoneNumber
      });
      common_vendor.index.showToast({
        title: "验证码已发送",
        icon: "success"
      });
    };
    const isAgree = common_vendor.ref(false);
    const toggleAgreement = () => {
      isAgree.value = !isAgree.value;
    };
    const showAgreement = () => {
      common_vendor.index.navigateTo({
        url: "/pages/aggrement/user-aggrement"
      });
    };
    const showPrivacy = () => {
      common_vendor.index.navigateTo({
        url: "/pages/aggrement/privacy-aggrement"
      });
    };
    const checkBeforeLogin = () => {
      if (!isAgree.value) {
        common_vendor.index.showToast({
          title: "请阅读并同意用户协议和隐私政策",
          icon: "none"
        });
        return false;
      }
      return true;
    };
    const fetchLogin = async () => {
      if (loginForm.value.phoneNumber === "" || loginForm.value.phoneNumber.length !== 11 || loginForm.value.msgCode === "" || loginForm.value.msgCode.length !== 6) {
        common_vendor.index.showToast({
          title: "请输入正确的手机号或者验证码",
          icon: "none"
        });
        return;
      }
      if (!checkBeforeLogin())
        return;
      const loginRes = await getWxCode();
      loginForm.value.wxCode = loginRes.code;
      if (!loginForm.value.wxCode) {
        common_vendor.index.showToast({
          title: "获取微信code失败",
          icon: "none"
        });
        return;
      }
      common_vendor.index.showLoading({
        title: "登录中...",
        mask: true
      });
      try {
        const res = await api_login.login(loginForm.value);
        utils_utils.setStorageSync("token", res.data.token);
        utils_utils.setStorageSync("wxUser", res.data.wxUser);
        common_vendor.index.hideLoading();
        common_vendor.index.showToast({
          title: "登录成功",
          icon: "success",
          duration: 1500
        });
        setTimeout(() => {
          common_vendor.index.switchTab({
            url: "/pages/home/<USER>"
          });
        }, 1500);
      } catch (error) {
        common_vendor.index.showToast({
          title: error.msg,
          icon: "none"
        });
      }
    };
    const handleWechatLogin = async (e) => {
      checkBeforeLogin();
      if (e.detail.errMsg === "getPhoneNumber:ok") {
        const phoneCode = e.detail.code;
        common_vendor.index.login({
          provider: "weixin",
          success: (res) => {
            const code = res.code;
            const params = {
              wxCode: code,
              phoneCode
            };
            common_vendor.index.showLoading({
              title: "登录中...",
              mask: true
            });
            api_login.loginWx(params).then((res2) => {
              utils_utils.setStorageSync("token", res2.data.token);
              utils_utils.setStorageSync("wxUser", res2.data.wxUser);
              common_vendor.index.hideLoading();
              common_vendor.index.showToast({
                title: "登录成功",
                icon: "success",
                duration: 1500
              });
              setTimeout(() => {
                common_vendor.index.switchTab({
                  url: "/pages/home/<USER>"
                });
              }, 1500);
            }).catch((err) => {
              common_vendor.index.hideLoading();
              common_vendor.index.showToast({
                title: "登录失败，请重试",
                icon: "none"
              });
            });
          },
          fail: (err) => {
            common_vendor.index.showToast({
              title: "获取微信授权失败",
              icon: "none"
            });
          }
        });
      } else {
        common_vendor.index.showToast({
          title: "获取手机号失败",
          icon: "none"
        });
      }
    };
    const getWxCode = () => {
      return new Promise((resolve, reject) => {
        common_vendor.index.login({
          provider: "weixin",
          success: resolve,
          fail: reject
        });
      });
    };
    common_vendor.onUnmounted(() => {
      if (countdownInterval) {
        clearInterval(countdownInterval);
      }
    });
    common_vendor.onMounted(() => {
      initSystemInfo();
    });
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_vendor.p({
          name: "arrow-left",
          size: "20",
          color: "#fff"
        }),
        b: common_vendor.o(handleBack),
        c: menuButtonTop.value + "px",
        d: menuButtonHeight.value + "px",
        e: common_assets._imports_0$4,
        f: loginForm.value.phoneNumber,
        g: common_vendor.o(($event) => loginForm.value.phoneNumber = $event.detail.value),
        h: loginForm.value.msgCode,
        i: common_vendor.o(($event) => loginForm.value.msgCode = $event.detail.value),
        j: common_vendor.t(buttonText.value),
        k: common_vendor.o(fetchCode),
        l: buttonDisabled.value,
        m: common_vendor.o(fetchLogin),
        n: isAgree.value
      }, isAgree.value ? {
        o: common_vendor.p({
          name: "checkmark",
          size: "22",
          color: "#fff"
        })
      } : {}, {
        p: isAgree.value ? 1 : "",
        q: common_vendor.o(toggleAgreement),
        r: common_vendor.o(showAgreement),
        s: common_vendor.o(showPrivacy),
        t: !isAgree.value
      }, !isAgree.value ? {
        v: common_vendor.p({
          name: "weixin-fill",
          size: "32",
          color: "#fff"
        }),
        w: common_vendor.o(checkBeforeLogin)
      } : {
        x: common_vendor.p({
          name: "weixin-fill",
          size: "32",
          color: "#fff"
        }),
        y: common_vendor.o(handleWechatLogin)
      });
    };
  }
};
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-e4e4508d"]]);
wx.createPage(MiniProgramPage);
