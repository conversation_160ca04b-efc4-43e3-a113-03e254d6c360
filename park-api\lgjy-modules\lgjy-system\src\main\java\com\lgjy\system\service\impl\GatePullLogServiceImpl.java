package com.lgjy.system.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.lgjy.system.mapper.GatePullLogMapper;
import com.lgjy.system.domain.GatePullLog;
import com.lgjy.system.service.IGatePullLogService;

/**
 * 道闸推送日志Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-08-03
 */
@Service
public class GatePullLogServiceImpl implements IGatePullLogService 
{
    @Autowired
    private GatePullLogMapper gatePullLogMapper;

    /**
     * 查询道闸推送日志
     * 
     * @param id 道闸推送日志主键
     * @return 道闸推送日志
     */
    @Override
    public GatePullLog selectGatePullLogById(String id)
    {
        return gatePullLogMapper.selectGatePullLogById(id);
    }

    /**
     * 查询道闸推送日志列表
     * 
     * @param gatePullLog 道闸推送日志
     * @return 道闸推送日志
     */
    @Override
    public List<GatePullLog> selectGatePullLogList(GatePullLog gatePullLog)
    {
        return gatePullLogMapper.selectGatePullLogList(gatePullLog);
    }

    /**
     * 删除道闸推送日志
     * 
     * @param id 道闸推送日志主键
     * @return 结果
     */
    @Override
    public int deleteGatePullLogById(String id)
    {
        return gatePullLogMapper.deleteGatePullLogById(id);
    }

    /**
     * 批量删除道闸推送日志
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    @Override
    public int deleteGatePullLogByIds(String[] ids)
    {
        return gatePullLogMapper.deleteGatePullLogByIds(ids);
    }

    /**
     * 清空道闸推送日志
     */
    @Override
    public void cleanGatePullLog()
    {
        gatePullLogMapper.cleanGatePullLog();
    }
}
