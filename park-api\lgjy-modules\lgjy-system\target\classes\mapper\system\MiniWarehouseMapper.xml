<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lgjy.system.mapper.MiniWarehouseMapper">

    <resultMap type="com.lgjy.system.domain.MiniWarehouse" id="MiniWarehouseResult">
        <result property="id" column="id" />
        <result property="projectName" column="project_name" />
        <result property="warehouseName" column="warehouse_name" />
        <result property="operatorId" column="operator_id" />
        <result property="operatorName" column="operator_name" />
        <result property="smartsType" column="smarts_type" />
        <result property="totalParking" column="total_parking" />
        <result property="parkingSpaceType" column="parking_space_type" />
        <result property="provinceCode" column="province_code" />
        <result property="cityCode" column="city_code" />
        <result property="areaCode" column="area_code" />
        <result property="address" column="address" />
        <result property="longitude" column="longitude" />
        <result property="latitude" column="latitude" />
        <result property="remark" column="remark" />
        <result property="status" column="status" />
        <result property="userId" column="user_id" />
        <result property="deleteFlag" column="delete_flag" />
        <result property="carouselImages" column="carousel_images" />
        <result property="parentId" column="parent_id" />
        <result property="createBy" column="create_by" />
        <result property="createTime" column="create_time" />
        <result property="updateBy" column="update_by" />
        <result property="updateTime" column="update_time" />
        <result property="createdBy" column="created_by" />
        <result property="updatedBy" column="updated_by" />
        <result property="provinceName" column="province_name" />
        <result property="cityName" column="city_name" />
        <result property="areaName" column="area_name" />
        <result property="leasePropertyNo" column="lease_property_no" />
        <result property="leaseAddress" column="lease_address" />
        <result property="leaseDetailAddress" column="lease_detail_address" />
        <result property="responsiblePerson" column="responsible_person" />
        <result property="contactInfo" column="contact_info" />
    </resultMap>

    <sql id="selectMiniWarehouseVo">
        select w.id, w.project_name, w.warehouse_name, w.operator_id, o.company_name as operator_name,
               w.smarts_type, w.total_parking, w.parking_space_type, w.province_code, w.city_code, w.area_code, w.address,
               w.longitude, w.latitude, w.remark, w.status, w.user_id, w.delete_flag, w.carousel_images,
               w.parent_id, w.create_by, w.create_time, w.update_by, w.update_time,
               w.lease_property_no, w.lease_address, w.lease_detail_address,
               w.responsible_person, w.contact_info,
               coalesce(cu.nick_name, cu.user_name, w.create_by) as created_by,
               coalesce(uu.nick_name, uu.user_name, w.update_by) as updated_by,
               p.area_name as province_name,
               c.area_name as city_name,
               a.area_name as area_name
        from mini_warehouse w
        left join mini_operator o on w.operator_id = o.id and o.delete_flag = 0
        left join sys_user cu on w.create_by = cu.user_id and cu.delete_flag = '0'
        left join sys_user uu on w.update_by = uu.user_id and uu.delete_flag = '0'
        left join sys_area p on w.province_code = p.area_code
        left join sys_area c on w.city_code = c.area_code
        left join sys_area a on w.area_code = a.area_code
    </sql>

    <select id="selectMiniWarehouseList" parameterType="com.lgjy.system.domain.MiniWarehouse" resultMap="MiniWarehouseResult">
        <include refid="selectMiniWarehouseVo"/>
        <where>
            w.delete_flag = 0
            <if test="projectName != null and projectName != ''"> and w.project_name like concat('%', #{projectName}, '%')</if>
            <if test="warehouseName != null and warehouseName != ''"> and w.warehouse_name like concat('%', #{warehouseName}, '%')</if>
            <if test="operatorId != null"> and w.operator_id = #{operatorId}</if>
            <if test="provinceCode != null and provinceCode != ''"> and w.province_code = #{provinceCode}</if>
            <if test="cityCode != null and cityCode != ''"> and w.city_code = #{cityCode}</if>
            <if test="areaCode != null and areaCode != ''"> and w.area_code = #{areaCode}</if>
            <if test="status != null"> and w.status = #{status}</if>
            <if test="parentId != null"> and w.parent_id = #{parentId}</if>
        </where>
        order by id desc
    </select>

    <select id="selectMiniWarehouseById" parameterType="Long" resultMap="MiniWarehouseResult">
        <include refid="selectMiniWarehouseVo"/>
        where w.id = #{id} and w.delete_flag = 0
    </select>

    <select id="selectMiniWarehouseAll" resultMap="MiniWarehouseResult">
        <include refid="selectMiniWarehouseVo"/>
        where w.delete_flag = 0 and w.parent_id = 0
        order by w.warehouse_name
    </select>

    <select id="selectMiniWarehouseByOperatorId" parameterType="Long" resultMap="MiniWarehouseResult">
        <include refid="selectMiniWarehouseVo"/>
        where w.delete_flag = 0 and w.operator_id = #{operatorId} and w.parent_id = 0
        order by w.warehouse_name
    </select>

    <select id="selectAllWarehouseOptions" resultMap="MiniWarehouseResult">
        <include refid="selectMiniWarehouseVo"/>
        where w.delete_flag = 0
        order by w.parent_id, w.warehouse_name
    </select>

    <select id="countWarehouseByOperatorId" parameterType="Long" resultType="int">
        select count(*) from mini_warehouse where operator_id = #{operatorId} and delete_flag = 0
    </select>

    <insert id="insertMiniWarehouse" parameterType="com.lgjy.system.domain.MiniWarehouse">
        insert into mini_warehouse
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="projectName != null and projectName != ''">project_name,</if>
            <if test="warehouseName != null and warehouseName != ''">warehouse_name,</if>
            <if test="operatorId != null">operator_id,</if>
            <if test="smartsType != null">smarts_type,</if>
            <if test="totalParking != null">total_parking,</if>
            <if test="parkingSpaceType != null">parking_space_type,</if>
            <if test="provinceCode != null">province_code,</if>
            <if test="cityCode != null">city_code,</if>
            <if test="areaCode != null">area_code,</if>
            <if test="address != null">address,</if>
            <if test="longitude != null">longitude,</if>
            <if test="latitude != null">latitude,</if>
            <if test="remark != null">remark,</if>
            <if test="status != null">status,</if>
            <if test="userId != null">user_id,</if>
            <if test="carouselImages != null">carousel_images,</if>
            <if test="parentId != null">parent_id,</if>
            <if test="leasePropertyNo != null">lease_property_no,</if>
            <if test="leaseAddress != null">lease_address,</if>
            <if test="leaseDetailAddress != null">lease_detail_address,</if>
            <if test="responsiblePerson != null">responsible_person,</if>
            <if test="contactInfo != null">contact_info,</if>
            <if test="createBy != null">create_by,</if>
            create_time
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="projectName != null and projectName != ''">#{projectName},</if>
            <if test="warehouseName != null and warehouseName != ''">#{warehouseName},</if>
            <if test="operatorId != null">#{operatorId},</if>
            <if test="smartsType != null">#{smartsType},</if>
            <if test="totalParking != null">#{totalParking},</if>
            <if test="parkingSpaceType != null">#{parkingSpaceType},</if>
            <if test="provinceCode != null">#{provinceCode},</if>
            <if test="cityCode != null">#{cityCode},</if>
            <if test="areaCode != null">#{areaCode},</if>
            <if test="address != null">#{address},</if>
            <if test="longitude != null">#{longitude},</if>
            <if test="latitude != null">#{latitude},</if>
            <if test="remark != null">#{remark},</if>
            <if test="status != null">#{status},</if>
            <if test="userId != null">#{userId},</if>
            <if test="carouselImages != null">#{carouselImages},</if>
            <if test="parentId != null">#{parentId},</if>
            <if test="leasePropertyNo != null">#{leasePropertyNo},</if>
            <if test="leaseAddress != null">#{leaseAddress},</if>
            <if test="leaseDetailAddress != null">#{leaseDetailAddress},</if>
            <if test="responsiblePerson != null">#{responsiblePerson},</if>
            <if test="contactInfo != null">#{contactInfo},</if>
            <if test="createBy != null">#{createBy},</if>
            now()
        </trim>
    </insert>

    <update id="updateMiniWarehouse" parameterType="com.lgjy.system.domain.MiniWarehouse">
        update mini_warehouse
        <trim prefix="SET" suffixOverrides=",">
            <if test="projectName != null and projectName != ''">project_name = #{projectName},</if>
            <if test="warehouseName != null and warehouseName != ''">warehouse_name = #{warehouseName},</if>
            <if test="operatorId != null">operator_id = #{operatorId},</if>
            <if test="smartsType != null">smarts_type = #{smartsType},</if>
            <if test="totalParking != null">total_parking = #{totalParking},</if>
            <if test="parkingSpaceType != null">parking_space_type = #{parkingSpaceType},</if>
            <if test="provinceCode != null">province_code = #{provinceCode},</if>
            <if test="cityCode != null">city_code = #{cityCode},</if>
            <if test="areaCode != null">area_code = #{areaCode},</if>
            <if test="address != null">address = #{address},</if>
            <if test="longitude != null">longitude = #{longitude},</if>
            <if test="latitude != null">latitude = #{latitude},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="status != null">status = #{status},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="carouselImages != null">carousel_images = #{carouselImages},</if>
            <if test="parentId != null">parent_id = #{parentId},</if>
            <if test="leasePropertyNo != null">lease_property_no = #{leasePropertyNo},</if>
            <if test="leaseAddress != null">lease_address = #{leaseAddress},</if>
            <if test="leaseDetailAddress != null">lease_detail_address = #{leaseDetailAddress},</if>
            <if test="responsiblePerson != null">responsible_person = #{responsiblePerson},</if>
            <if test="contactInfo != null">contact_info = #{contactInfo},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            update_time = now()
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteMiniWarehouseById" parameterType="Long">
        update mini_warehouse set delete_flag = 1 where id = #{id}
    </delete>

    <delete id="deleteMiniWarehouseByIds" parameterType="String">
        update mini_warehouse set delete_flag = 1 where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <!-- 统计场库下的子场库数量 -->
    <select id="countChildWarehousesByParentId" parameterType="Long" resultType="int">
        select count(*)
        from mini_warehouse
        where parent_id = #{parentId}
          and parent_id != 0
          and delete_flag = 0
    </select>

    <!-- 查询所有子场库列表（用于下拉选择） -->
    <select id="selectChildWarehouseOptions" resultMap="MiniWarehouseResult">
        <include refid="selectMiniWarehouseVo"/>
        where w.delete_flag = 0 and w.status = 1 and w.parent_id != 0
        order by w.warehouse_name
    </select>

    <!-- 根据父场库ID查询子场库列表 -->
    <select id="selectChildWarehouseOptionsByParentId" parameterType="Long" resultMap="MiniWarehouseResult">
        <include refid="selectMiniWarehouseVo"/>
        where w.delete_flag = 0 and w.status = 1 and w.parent_id = #{parentId} and w.parent_id != 0
        order by w.warehouse_name
    </select>

</mapper>
