/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* uni.scss */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.warehouse-detail.data-v-4b6d5a52 {
  background-color: #f5f5f5;
  min-height: 100vh;
}
.carousel-container.data-v-4b6d5a52 {
  width: 100%;
  height: 400rpx;
}
.carousel-container .carousel.data-v-4b6d5a52 {
  width: 100%;
  height: 100%;
}
.carousel-container .carousel .carousel-image.data-v-4b6d5a52 {
  width: 100%;
  height: 100%;
}
.info-section.data-v-4b6d5a52 {
  background: white;
  margin: 20rpx 0;
  padding: 30rpx;
}
.info-section.data-v-4b6d5a52:first-child {
  margin-top: 0;
}
.warehouse-header.data-v-4b6d5a52 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}
.warehouse-header .warehouse-name.data-v-4b6d5a52 {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  flex: 1;
}
.warehouse-header .status-tag.data-v-4b6d5a52 {
  padding: 8rpx 20rpx;
  background: #f0f0f0;
  color: #999;
  border-radius: 20rpx;
  font-size: 24rpx;
}
.warehouse-header .status-tag.active.data-v-4b6d5a52 {
  background: #e8f5e8;
  color: #52c41a;
}
.address-row.data-v-4b6d5a52, .contact-row.data-v-4b6d5a52 {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}
.address-row.data-v-4b6d5a52:last-child, .contact-row.data-v-4b6d5a52:last-child {
  border-bottom: none;
  margin-bottom: 0;
}
.address-row .icon.data-v-4b6d5a52, .contact-row .icon.data-v-4b6d5a52 {
  width: 32rpx;
  height: 32rpx;
  margin-right: 20rpx;
}
.address-row .address-text.data-v-4b6d5a52, .address-row .contact-text.data-v-4b6d5a52, .contact-row .address-text.data-v-4b6d5a52, .contact-row .contact-text.data-v-4b6d5a52 {
  flex: 1;
  color: #333;
  font-size: 28rpx;
  line-height: 1.4;
}
.address-row .nav-text.data-v-4b6d5a52, .address-row .call-text.data-v-4b6d5a52, .contact-row .nav-text.data-v-4b6d5a52, .contact-row .call-text.data-v-4b6d5a52 {
  color: #1890ff;
  font-size: 28rpx;
}
.section-title.data-v-4b6d5a52 {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 30rpx;
  padding-bottom: 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
}
.remark-content .remark-text.data-v-4b6d5a52 {
  font-size: 28rpx;
  color: #333;
  line-height: 1.6;
}