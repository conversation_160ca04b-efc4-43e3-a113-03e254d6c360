"use strict";
const common_vendor = require("../../common/vendor.js");
const common_assets = require("../../common/assets.js");
const api_warehouse = require("../../api/warehouse.js");
const _sfc_main = {
  __name: "warehouseDetail",
  setup(__props) {
    const id = common_vendor.ref(0);
    const warehouseDetail = common_vendor.ref({});
    const carouselImages = common_vendor.computed(() => {
      if (!warehouseDetail.value.carouselImages)
        return [];
      try {
        const images = JSON.parse(warehouseDetail.value.carouselImages);
        return Array.isArray(images) ? images : [images];
      } catch (e) {
        return [];
      }
    });
    common_vendor.onLoad((options) => {
      console.log(options);
      id.value = options.id;
      fetchWarehouseDetail();
    });
    const fetchWarehouseDetail = async () => {
      const res = await api_warehouse.getWarehouseDetail(id.value);
      warehouseDetail.value = res.data;
      console.log(warehouseDetail.value);
    };
    const openMap = () => {
      const { latitude, longitude, warehouseName, address } = warehouseDetail.value;
      common_vendor.index.openLocation({
        latitude: parseFloat(latitude),
        longitude: parseFloat(longitude),
        name: warehouseName,
        address,
        success: () => {
          console.log("打开地图成功");
        },
        fail: (err) => {
          console.log("打开地图失败", err);
          common_vendor.index.showToast({
            title: "无法打开地图",
            icon: "none"
          });
        }
      });
    };
    const makeCall = () => {
      common_vendor.index.makePhoneCall({
        phoneNumber: warehouseDetail.value.managerPhone,
        success: () => {
          console.log("拨打电话成功");
        },
        fail: (err) => {
          console.log("拨打电话失败", err);
        }
      });
    };
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_vendor.f(carouselImages.value, (image, index, i0) => {
          return {
            a: image,
            b: index
          };
        }),
        b: carouselImages.value.length > 1,
        c: common_vendor.t(warehouseDetail.value.warehouseName || warehouseDetail.value.projectName),
        d: common_vendor.t(warehouseDetail.value.status === 1 ? "营业中" : "暂停营业"),
        e: warehouseDetail.value.status === 1 ? 1 : "",
        f: common_assets._imports_0$2,
        g: common_vendor.t(warehouseDetail.value.address),
        h: common_vendor.o(openMap),
        i: warehouseDetail.value.managerPhone
      }, warehouseDetail.value.managerPhone ? {
        j: common_assets._imports_1$1,
        k: common_vendor.t(warehouseDetail.value.managerPhone),
        l: common_vendor.o(makeCall)
      } : {}, {
        m: warehouseDetail.value.remark
      }, warehouseDetail.value.remark ? {
        n: common_vendor.t(warehouseDetail.value.remark)
      } : {});
    };
  }
};
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-4b6d5a52"]]);
wx.createPage(MiniProgramPage);
