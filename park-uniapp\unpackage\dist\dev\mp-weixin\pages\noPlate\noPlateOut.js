"use strict";
const common_vendor = require("../../common/vendor.js");
const common_assets = require("../../common/assets.js");
const api_login = require("../../api/login.js");
const api_parkingOrder = require("../../api/parkingOrder.js");
const _sfc_main = {
  __name: "noPlateOut",
  setup(__props) {
    const gateNo = common_vendor.ref("");
    const warehouseId = common_vendor.ref("");
    const openid = common_vendor.ref("");
    const scanTime = common_vendor.ref(0);
    common_vendor.onLoad((options) => {
      console.log("页面参数:", options);
      const qrUrl = decodeURIComponent(options.q || "");
      const scancode_time = parseInt(options.scancode_time || 0);
      console.log("二维码原始链接:", qrUrl);
      console.log("扫码时间:", scancode_time);
      if (qrUrl) {
        parseQrParamsManually(qrUrl);
      } else {
        gateNo.value = options.gateNo || "";
        warehouseId.value = options.warehouseId || "";
        if (gateNo.value && warehouseId.value) {
          handleScanParams(gateNo.value, warehouseId.value);
        }
      }
      scanTime.value = scancode_time;
    });
    const parseQrParamsManually = (url) => {
      try {
        const queryString = url.split("?")[1];
        if (!queryString) {
          throw new Error("URL中没有查询参数");
        }
        const params = {};
        queryString.split("&").forEach((param) => {
          const [key, value] = param.split("=");
          if (key && value) {
            params[decodeURIComponent(key)] = decodeURIComponent(value);
          }
        });
        console.log("手动解析的参数:", params);
        gateNo.value = params.gateNo || "";
        warehouseId.value = params.warehouseId || "";
        handleScanParams(gateNo.value, warehouseId.value);
      } catch (error) {
        console.error("手动解析URL参数也失败:", error);
        common_vendor.index.showToast({
          title: "二维码格式错误,请重新扫码～",
          icon: "none",
          duration: 2e3
        });
      }
    };
    const handleScanParams = (gateNoParam, warehouseIdParam) => {
      if (gateNoParam && warehouseIdParam) {
        setTimeout(() => {
          showConfirmModal();
        }, 500);
      } else {
        common_vendor.index.showToast({
          title: "参数错误,请重新扫码～",
          icon: "none",
          duration: 2e3
        });
      }
    };
    const noPlateOutPost = () => {
      common_vendor.index.showLoading({
        title: "出场中...",
        mask: true
      });
      common_vendor.index.login({
        provider: "weixin",
        success: (res) => {
          api_login.getOpenid({ wxCode: res.code }).then((res2) => {
            openid.value = res2.data;
            if (!openid.value) {
              common_vendor.index.hideLoading();
              common_vendor.index.showToast({
                title: "网络异常,请重新扫码～",
                icon: "none",
                duration: 2e3
              });
              return;
            }
            let params = {
              openId: openid.value,
              gateNo: gateNo.value,
              warehouseId: warehouseId.value
            };
            api_parkingOrder.noPlateOut(params).then((res3) => {
              if (res3.code == 200 && res3.msg == "000") {
                common_vendor.index.hideLoading();
                common_vendor.index.showToast({
                  title: "金额为0,出场成功",
                  icon: "success",
                  duration: 2e3
                });
                return;
              }
              common_vendor.index.requestPayment({
                timeStamp: res3.data.timeStamp,
                nonceStr: res3.data.nonceStr,
                package: res3.data.package,
                signType: res3.data.signType,
                paySign: res3.data.paySign,
                success: function(result) {
                  common_vendor.index.hideLoading();
                  common_vendor.index.showToast({
                    title: "支付成功~",
                    icon: "success",
                    duration: 2e3,
                    complete: function() {
                      setTimeout(() => {
                        common_vendor.index.navigateBack();
                      }, 1e3);
                    }
                  });
                },
                fail: function(err) {
                  console.log("支付失败的回调：", err);
                  common_vendor.index.showToast({
                    title: "支付失败，请重试",
                    icon: "none",
                    duration: 2e3
                  });
                },
                complete: function(res4) {
                  common_vendor.index.hideLoading();
                }
              });
            }).catch((err) => {
              console.error("无牌车出场失败:", err);
              common_vendor.index.hideLoading();
              common_vendor.index.showToast({
                title: err.msg || "出场失败,请重新扫码～",
                icon: "none",
                duration: 2e3
              });
            });
          }).catch((err) => {
            console.error("获取openid失败:", err);
            common_vendor.index.hideLoading();
            common_vendor.index.showToast({
              title: "网络异常,请重新扫码～",
              icon: "none",
              duration: 2e3
            });
          });
        },
        fail: (err) => {
          console.error("微信登录失败:", err);
          common_vendor.index.hideLoading();
          common_vendor.index.showToast({
            title: "微信登录失败，请重试～",
            icon: "none",
            duration: 2e3
          });
        },
        complete: () => {
          common_vendor.index.hideLoading();
        }
      });
    };
    const showConfirmModal = () => {
      common_vendor.index.showModal({
        title: "确认出场",
        content: "您确定是无牌车，且要出场吗？",
        cancelText: "取消",
        confirmText: "确认",
        success: (res) => {
          if (res.confirm) {
            noPlateOutPost();
          } else if (res.cancel) {
            common_vendor.index.showToast({
              title: "已经取消出场",
              icon: "none",
              duration: 2e3
            });
          }
        }
      });
    };
    return (_ctx, _cache) => {
      return {
        a: common_assets._imports_0$5
      };
    };
  }
};
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-dc27b404"]]);
wx.createPage(MiniProgramPage);
