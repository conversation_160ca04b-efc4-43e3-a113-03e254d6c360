/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* uni.scss */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.warehouse-popup.data-v-e7c75adf {
  background-color: #fff;
}
.warehouse-popup .popup-header.data-v-e7c75adf {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx;
  border-bottom: 1rpx solid #e2e2e2;
}
.warehouse-popup .popup-header .popup-title.data-v-e7c75adf {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}
.warehouse-popup .warehouse-list.data-v-e7c75adf {
  padding: 16rpx 0;
}
.warehouse-popup .warehouse-list .warehouse-item.data-v-e7c75adf {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx;
  border-bottom: 1rpx solid #f8f9fa;
}
.warehouse-popup .warehouse-list .warehouse-item.data-v-e7c75adf:last-child {
  border-bottom: none;
}
.warehouse-popup .warehouse-list .warehouse-item.active.data-v-e7c75adf {
  background-color: #f0f8ff;
}
.warehouse-popup .warehouse-list .warehouse-item.active .warehouse-item-name.data-v-e7c75adf {
  color: #2da0fe;
  font-weight: 500;
}
.warehouse-popup .warehouse-list .warehouse-item.data-v-e7c75adf:active {
  background-color: #f5f5f5;
}
.warehouse-popup .warehouse-list .warehouse-item .warehouse-item-name.data-v-e7c75adf {
  font-size: 30rpx;
  color: #333;
  text-align: center;
  flex: 1;
}