<template>
  <!-- 环形统计图 -->
  <div class="chart-section">
    <div class="section-header">
      <h3>车流量统计</h3>
    </div>
    <div ref="trafficChartRef" class="chart-container"></div>
  </div>
</template>

<script setup>
import * as echarts from 'echarts'
import { ref, onMounted, onUnmounted, watch } from 'vue'

// 定义props
const props = defineProps({
  statisticsData: {
    type: Object,
    required: true,
    default: () => ({
      todayEntry: 0,
      todayExit: 0
    })
  }
})

// 图表引用
const trafficChartRef = ref()
let trafficChart = null

// 初始化环形图
const initTrafficChart = () => {
  if (!trafficChartRef.value) return

  trafficChart = echarts.init(trafficChartRef.value)

  const option = {
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)'
    },
    legend: {
      orient: 'horizontal',
      bottom: '10%',
      textStyle: {
        color: '#333',
        fontSize: 12
      }
    },
    series: [
      {
        name: '车流量',
        type: 'pie',
        radius: ['40%', '70%'],
        center: ['50%', '45%'],
        avoidLabelOverlap: false,
        label: {
          show: false,
          position: 'center'
        },
        emphasis: {
          label: {
            show: true,
            fontSize: '18',
            fontWeight: 'bold'
          }
        },
        labelLine: {
          show: false
        },
        data: [
          {
            value: props.statisticsData.todayEntry,
            name: '当日进场车次',
            itemStyle: { color: '#67C23A' }
          },
          {
            value: props.statisticsData.todayExit,
            name: '当日出场车次',
            itemStyle: { color: '#FFA500' }
          }
        ]
      }
    ]
  }

  trafficChart.setOption(option)
}

// 更新图表数据
const updateChart = () => {
  if (!trafficChart) return

  const option = {
    series: [
      {
        data: [
          {
            value: props.statisticsData.todayEntry,
            name: '当日进场车次',
            itemStyle: { color: '#67C23A' }
          },
          {
            value: props.statisticsData.todayExit,
            name: '当日出场车次',
            itemStyle: { color: '#FFA500' }
          }
        ]
      }
    ]
  }

  trafficChart.setOption(option)
}

// 窗口大小改变时重新调整图表
const handleResize = () => {
  if (trafficChart) {
    trafficChart.resize()
  }
}

// 监听数据变化
watch(() => props.statisticsData, () => {
  updateChart()
}, { deep: true })

onMounted(() => {
  initTrafficChart()
  window.addEventListener('resize', handleResize)
})

onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
  if (trafficChart) {
    trafficChart.dispose()
  }
})
</script>

<style scoped>
/* 环形统计图区域 */
.chart-section {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  padding: 20px;
  height: 280px;
}

/* 星空主题图表区域 - 只在暗黑模式下生效 */
html.dark .chart-section {
  background: rgba(255, 255, 255, 0.95) !important;
  border-radius: 16px !important;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3) !important;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 2px solid #e3f2fd;
}

.section-header h3 {
  margin: 0;
  font-size: 16px;
  color: white;
  background: #2B6CB0;
  padding: 8px 16px;
  border-radius: 4px;
  font-weight: 600;
}

.chart-container {
  height: 200px;
  width: 100%;
}
</style>
