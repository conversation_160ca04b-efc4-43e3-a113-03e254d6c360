<template>
  <!-- 星空背景 -->
  <div class="starfield-background">
    <!-- 星星粒子 -->
    <div class="stars-container">
      <div v-for="n in 150" :key="n" class="star" :style="getStarStyle()"></div>
    </div>

    <!-- 流星效果 -->
    <div class="meteors-container">
      <div v-for="n in 3" :key="n" class="meteor" :style="getMeteorStyle(n)"></div>
    </div>

    <!-- 极光效果 -->
    <div class="aurora-container">
      <div class="aurora aurora-1"></div>
      <div class="aurora aurora-2"></div>
    </div>
  </div>
</template>

<script setup>
// 星空效果方法
const getStarStyle = () => {
  return {
    left: Math.random() * 100 + '%',
    top: Math.random() * 100 + '%',
    width: Math.random() * 3 + 1 + 'px',
    height: Math.random() * 3 + 1 + 'px',
    animationDuration: Math.random() * 3 + 2 + 's',
    animationDelay: Math.random() * 2 + 's'
  }
}

const getMeteorStyle = (index) => {
  return {
    left: Math.random() * 100 + '%',
    top: Math.random() * 50 + '%',
    animationDuration: Math.random() * 3 + 8 + 's',
    animationDelay: index * 3 + 's'
  }
}
</script>

<style scoped>
/* 星空背景容器 - 只在暗黑模式下显示 */
.starfield-background {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
  pointer-events: none;
}

/* 非暗黑模式下隐藏星空背景 */
html:not(.dark) .starfield-background {
  display: none;
}

/* 星星容器 */
.stars-container {
  position: absolute;
  width: 100%;
  height: 100%;
}

.star {
  position: absolute;
  background: #ffffff;
  border-radius: 50%;
  animation: twinkle infinite ease-in-out alternate;
  box-shadow: 0 0 6px #ffffff;
}

/* 星星闪烁动画 */
@keyframes twinkle {
  0% {
    opacity: 0.3;
    transform: scale(1);
  }
  100% {
    opacity: 1;
    transform: scale(1.2);
  }
}

/* 流星容器 */
.meteors-container {
  position: absolute;
  width: 100%;
  height: 100%;
}

.meteor {
  position: absolute;
  width: 300px;
  height: 2px;
  background: linear-gradient(90deg, transparent, #ffffff, transparent);
  border-radius: 50%;
  animation: meteor infinite linear;
  opacity: 0;
}

/* 流星动画 */
@keyframes meteor {
  0% {
    transform: translateX(-100px) translateY(100px);
    opacity: 0;
  }
  10% { opacity: 1; }
  90% { opacity: 1; }
  100% {
    transform: translateX(100vw) translateY(-100px);
    opacity: 0;
  }
}

/* 极光容器 */
.aurora-container {
  position: absolute;
  width: 100%;
  height: 100%;
}

.aurora {
  position: absolute;
  width: 200%;
  height: 100%;
  opacity: 0.3;
  animation: aurora infinite ease-in-out alternate;
}

.aurora-1 {
  background: linear-gradient(45deg,
    rgba(64, 224, 208, 0.1) 0%,
    transparent 25%,
    transparent 50%,
    rgba(138, 43, 226, 0.1) 75%,
    transparent 100%);
  animation-duration: 8s;
  left: -50%;
}

.aurora-2 {
  background: linear-gradient(-45deg,
    transparent 0%,
    rgba(100, 200, 255, 0.1) 25%,
    transparent 50%,
    rgba(255, 100, 200, 0.1) 75%,
    transparent 100%);
  animation-duration: 12s;
  animation-delay: 4s;
  left: -50%;
}

/* 极光动画 */
@keyframes aurora {
  0%, 100% {
    opacity: 0.2;
    transform: translateX(0) scaleX(1);
  }
  50% {
    opacity: 0.6;
    transform: translateX(10%) scaleX(1.2);
  }
}
</style>
