<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lgjy.wx.mapper.WxInvoiceReverseRecordMapper">
    <sql id="selectSicvInvoiceReverseRecordVo">
        select id,warehouse_id,
               user_id,
               invoice_id,
               invoice_type,
               red_confirmed_uuid,
               status,
               reverse_invoice_no,
               reverse_invoice_code,
               invoice_no,
               invoice_code,
               reverse_date,
               qr_code_id,
               mid,
               tid,
               trade_id,
               order_date,
               tax_method,
               deduction_amount,
               total_price_including_tax,
               total_tax,
               total_price,
               notify_mobile_no,
               pdf_url,
               pdf_preview_url,
               remark,
               create_time,
               update_time
        from mini_invoice_reverse_record
    </sql>

    <select id="selectReverseRecordByInvoiceId" resultType="com.lgjy.wx.domain.WxInvoiceReverseRecord">
        <include refid="selectSicvInvoiceReverseRecordVo"/>
        where invoice_id = #{invoiceId}
    </select>

    <insert id="insertWxInvoiceReverseRecord">
        insert into mini_invoice_reverse_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="warehouseId != null">warehouse_id,</if>
            <if test="userId != null">user_id,</if>
            <if test="invoiceId != null">invoice_id,</if>
            <if test="invoiceType != null">invoice_type,</if>
            <if test="redConfirmedUuid != null">red_confirmed_uuid,</if>
            <if test="status != null">status,</if>
            <if test="reverseInvoiceNo != null">reverse_invoice_no,</if>
            <if test="reverseInvoiceCode != null">reverse_invoice_code,</if>
            <if test="invoiceNo != null">invoice_no,</if>
            <if test="invoiceCode != null">invoice_code,</if>
            <if test="reverseDate != null">reverse_date,</if>
            <if test="qrCodeId != null">qr_code_id,</if>
            <if test="mid != null">mid,</if>
            <if test="tid != null">tid,</if>
            <if test="tradeId != null">trade_id,</if>
            <if test="orderDate != null">order_date,</if>
            <if test="taxMethod != null">tax_method,</if>
            <if test="deductionAmount != null">deduction_amount,</if>
            <if test="totalPriceIncludingTax != null">total_price_including_tax,</if>
            <if test="totalTax != null">total_tax,</if>
            <if test="totalPrice != null">total_price,</if>
            <if test="notifyMobileNo != null">notify_mobile_no,</if>
            <if test="pdfUrl != null">pdf_url,</if>
            <if test="pdfPreviewUrl != null">pdf_preview_url,</if>
            <if test="remark != null">remark,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="warehouseId != null">#{warehouseId},</if>
            <if test="userId != null">#{userId},</if>
            <if test="invoiceId != null">#{invoiceId},</if>
            <if test="invoiceType != null">#{invoiceType},</if>
            <if test="redConfirmedUuid != null">#{redConfirmedUuid},</if>
            <if test="status != null">#{status},</if>
            <if test="reverseInvoiceNo != null">#{reverseInvoiceNo},</if>
            <if test="reverseInvoiceCode != null">#{reverseInvoiceCode},</if>
            <if test="invoiceNo != null">#{invoiceNo},</if>
            <if test="invoiceCode != null">#{invoiceCode},</if>
            <if test="reverseDate != null">#{reverseDate},</if>
            <if test="qrCodeId != null">#{qrCodeId},</if>
            <if test="mid != null">#{mid},</if>
            <if test="tid != null">#{tid},</if>
            <if test="tradeId != null">#{tradeId},</if>
            <if test="orderDate != null">#{orderDate},</if>
            <if test="taxMethod != null">#{taxMethod},</if>
            <if test="deductionAmount != null">#{deductionAmount},</if>
            <if test="totalPriceIncludingTax != null">#{totalPriceIncludingTax},</if>
            <if test="totalTax != null">#{totalTax},</if>
            <if test="totalPrice != null">#{totalPrice},</if>
            <if test="notifyMobileNo != null">#{notifyMobileNo},</if>
            <if test="pdfUrl != null">#{pdfUrl},</if>
            <if test="pdfPreviewUrl != null">#{pdfPreviewUrl},</if>
            <if test="remark != null">#{remark},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
        </trim>
    </insert>

    <update id="updateWxInvoiceReverseRecord">
        update mini_invoice_reverse_record
        <trim prefix="SET" suffixOverrides=",">
            <if test="warehouseId != null">warehouse_id = #{warehouseId},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="invoiceId != null">invoice_id = #{invoiceId},</if>
            <if test="invoiceType != null">invoice_type = #{invoiceType},</if>
            <if test="redConfirmedUuid != null">red_confirmed_uuid = #{redConfirmedUuid},</if>
            <if test="status != null">status = #{status},</if>
            <if test="reverseInvoiceNo != null">reverse_invoice_no = #{reverseInvoiceNo},</if>
            <if test="reverseInvoiceCode != null">reverse_invoice_code = #{reverseInvoiceCode},</if>
            <if test="invoiceNo != null">invoice_no = #{invoiceNo},</if>
            <if test="invoiceCode != null">invoice_code = #{invoiceCode},</if>
            <if test="reverseDate != null">reverse_date = #{reverseDate},</if>
            <if test="qrCodeId != null">qr_code_id = #{qrCodeId},</if>
            <if test="mid != null">mid = #{mid},</if>
            <if test="tid != null">tid = #{tid},</if>
            <if test="tradeId != null">trade_id = #{tradeId},</if>
            <if test="orderDate != null">order_date = #{orderDate},</if>
            <if test="taxMethod != null">tax_method = #{taxMethod},</if>
            <if test="deductionAmount != null">deduction_amount = #{deductionAmount},</if>
            <if test="totalPriceIncludingTax != null">total_price_including_tax = #{totalPriceIncludingTax},</if>
            <if test="totalTax != null">total_tax = #{totalTax},</if>
            <if test="totalPrice != null">total_price = #{totalPrice},</if>
            <if test="notifyMobileNo != null">notify_mobile_no = #{notifyMobileNo},</if>
            <if test="pdfUrl != null">pdf_url = #{pdfUrl},</if>
            <if test="pdfPreviewUrl != null">pdf_preview_url = #{pdfPreviewUrl},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            update_time = sysdate()
        </trim>
        where id = #{id}
    </update>
</mapper>