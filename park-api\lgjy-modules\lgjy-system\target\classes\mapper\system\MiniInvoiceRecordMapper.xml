<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lgjy.system.mapper.MiniInvoiceRecordMapper">
    
    <resultMap type="MiniInvoiceRecord" id="MiniInvoiceRecordResult">
        <result property="id"    column="id"    />
        <result property="userId"    column="user_id"    />
        <result property="warehouseId"    column="warehouse_id"    />
        <result property="warehouseName"    column="warehouse_name"    />
        <result property="functionType"    column="function_type"    />
        <result property="invoiceType"    column="invoice_type"    />
        <result property="invoiceNo"    column="invoice_no"    />
        <result property="invoiceCode"    column="invoice_code"    />
        <result property="qrCodeId"    column="qr_code_id"    />
        <result property="issueDate"    column="issue_date"    />
        <result property="totalMoney"    column="total_money"    />
        <result property="totalTax"    column="total_tax"    />
        <result property="invoiceTitleContent"    column="invoice_title_content"    />
        <result property="unitDutyParagraph"    column="unit_duty_paragraph"    />
        <result property="registerAddress"    column="register_address"    />
        <result property="registerPhone"    column="register_phone"    />
        <result property="depositBank"    column="deposit_bank"    />
        <result property="bankAccount"    column="bank_account"    />
        <result property="notifyMobileNo"    column="notify_mobile_no"    />
        <result property="notifyEmail"    column="notify_email"    />
        <result property="pdfUrl"    column="pdf_url"    />
        <result property="pdfPreviewUrl"    column="pdf_preview_url"    />
        <result property="tradeId"    column="trade_id"    />
        <result property="orderDate"    column="order_date"    />
        <result property="status"    column="status"    />
        <result property="mid"    column="mid"    />
        <result property="tid"    column="tid"    />
        <result property="reverseDate"    column="reverse_date"    />
        <result property="remark"    column="remark"    />
        <result property="reopenSign"    column="reopen_sign"    />
        <result property="oldTradeId"    column="old_trade_id"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectMiniInvoiceRecordVo">
        select mir.id, mir.user_id, mir.warehouse_id, mw.warehouse_name, mir.function_type, mir.invoice_type, 
               mir.invoice_no, mir.invoice_code, mir.qr_code_id, mir.issue_date, mir.total_money, mir.total_tax, 
               mir.invoice_title_content, mir.unit_duty_paragraph, mir.register_address, mir.register_phone, 
               mir.deposit_bank, mir.bank_account, mir.notify_mobile_no, mir.notify_email, mir.pdf_url, 
               mir.pdf_preview_url, mir.trade_id, mir.order_date, mir.status, mir.mid, mir.tid, 
               mir.reverse_date, mir.remark, mir.reopen_sign, mir.old_trade_id, mir.create_time, mir.update_time 
        from mini_invoice_record mir
        left join mini_warehouse mw on mir.warehouse_id = mw.id
    </sql>

    <select id="selectMiniInvoiceRecordList" parameterType="MiniInvoiceRecord" resultMap="MiniInvoiceRecordResult">
        <include refid="selectMiniInvoiceRecordVo"/>
        <where>  
            <if test="userId != null "> and mir.user_id = #{userId}</if>
            <if test="warehouseId != null "> and mir.warehouse_id = #{warehouseId}</if>
            <if test="functionType != null "> and mir.function_type = #{functionType}</if>
            <if test="invoiceType != null "> and mir.invoice_type = #{invoiceType}</if>
            <if test="invoiceNo != null  and invoiceNo != ''"> and mir.invoice_no like concat('%', #{invoiceNo}, '%')</if>
            <if test="invoiceCode != null  and invoiceCode != ''"> and mir.invoice_code like concat('%', #{invoiceCode}, '%')</if>
            <if test="tradeId != null  and tradeId != ''"> and mir.trade_id like concat('%', #{tradeId}, '%')</if>
            <if test="status != null  and status != ''"> and mir.status = #{status}</if>
            <if test="invoiceTitleContent != null  and invoiceTitleContent != ''"> and mir.invoice_title_content like concat('%', #{invoiceTitleContent}, '%')</if>
            <if test="params.beginTime != null and params.beginTime != ''"><!-- 开始时间检索 -->
                and date_format(mir.create_time,'%y%m%d') &gt;= date_format(#{params.beginTime},'%y%m%d')
            </if>
            <if test="params.endTime != null and params.endTime != ''"><!-- 结束时间检索 -->
                and date_format(mir.create_time,'%y%m%d') &lt;= date_format(#{params.endTime},'%y%m%d')
            </if>
        </where>
        order by mir.create_time desc
    </select>
    
    <select id="selectMiniInvoiceRecordById" parameterType="Long" resultMap="MiniInvoiceRecordResult">
        <include refid="selectMiniInvoiceRecordVo"/>
        where mir.id = #{id}
    </select>
        
    <insert id="insertMiniInvoiceRecord" parameterType="MiniInvoiceRecord" useGeneratedKeys="true" keyProperty="id">
        insert into mini_invoice_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userId != null">user_id,</if>
            <if test="warehouseId != null">warehouse_id,</if>
            <if test="functionType != null">function_type,</if>
            <if test="invoiceType != null">invoice_type,</if>
            <if test="invoiceNo != null">invoice_no,</if>
            <if test="invoiceCode != null">invoice_code,</if>
            <if test="qrCodeId != null">qr_code_id,</if>
            <if test="issueDate != null">issue_date,</if>
            <if test="totalMoney != null">total_money,</if>
            <if test="totalTax != null">total_tax,</if>
            <if test="invoiceTitleContent != null">invoice_title_content,</if>
            <if test="unitDutyParagraph != null">unit_duty_paragraph,</if>
            <if test="registerAddress != null">register_address,</if>
            <if test="registerPhone != null">register_phone,</if>
            <if test="depositBank != null">deposit_bank,</if>
            <if test="bankAccount != null">bank_account,</if>
            <if test="notifyMobileNo != null">notify_mobile_no,</if>
            <if test="notifyEmail != null">notify_email,</if>
            <if test="pdfUrl != null">pdf_url,</if>
            <if test="pdfPreviewUrl != null">pdf_preview_url,</if>
            <if test="tradeId != null">trade_id,</if>
            <if test="orderDate != null">order_date,</if>
            <if test="status != null">status,</if>
            <if test="mid != null">mid,</if>
            <if test="tid != null">tid,</if>
            <if test="reverseDate != null">reverse_date,</if>
            <if test="remark != null">remark,</if>
            <if test="reopenSign != null">reopen_sign,</if>
            <if test="oldTradeId != null">old_trade_id,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userId != null">#{userId},</if>
            <if test="warehouseId != null">#{warehouseId},</if>
            <if test="functionType != null">#{functionType},</if>
            <if test="invoiceType != null">#{invoiceType},</if>
            <if test="invoiceNo != null">#{invoiceNo},</if>
            <if test="invoiceCode != null">#{invoiceCode},</if>
            <if test="qrCodeId != null">#{qrCodeId},</if>
            <if test="issueDate != null">#{issueDate},</if>
            <if test="totalMoney != null">#{totalMoney},</if>
            <if test="totalTax != null">#{totalTax},</if>
            <if test="invoiceTitleContent != null">#{invoiceTitleContent},</if>
            <if test="unitDutyParagraph != null">#{unitDutyParagraph},</if>
            <if test="registerAddress != null">#{registerAddress},</if>
            <if test="registerPhone != null">#{registerPhone},</if>
            <if test="depositBank != null">#{depositBank},</if>
            <if test="bankAccount != null">#{bankAccount},</if>
            <if test="notifyMobileNo != null">#{notifyMobileNo},</if>
            <if test="notifyEmail != null">#{notifyEmail},</if>
            <if test="pdfUrl != null">#{pdfUrl},</if>
            <if test="pdfPreviewUrl != null">#{pdfPreviewUrl},</if>
            <if test="tradeId != null">#{tradeId},</if>
            <if test="orderDate != null">#{orderDate},</if>
            <if test="status != null">#{status},</if>
            <if test="mid != null">#{mid},</if>
            <if test="tid != null">#{tid},</if>
            <if test="reverseDate != null">#{reverseDate},</if>
            <if test="remark != null">#{remark},</if>
            <if test="reopenSign != null">#{reopenSign},</if>
            <if test="oldTradeId != null">#{oldTradeId},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateMiniInvoiceRecord" parameterType="MiniInvoiceRecord">
        update mini_invoice_record
        <trim prefix="SET" suffixOverrides=",">
            <if test="userId != null">user_id = #{userId},</if>
            <if test="warehouseId != null">warehouse_id = #{warehouseId},</if>
            <if test="functionType != null">function_type = #{functionType},</if>
            <if test="invoiceType != null">invoice_type = #{invoiceType},</if>
            <if test="invoiceNo != null">invoice_no = #{invoiceNo},</if>
            <if test="invoiceCode != null">invoice_code = #{invoiceCode},</if>
            <if test="qrCodeId != null">qr_code_id = #{qrCodeId},</if>
            <if test="issueDate != null">issue_date = #{issueDate},</if>
            <if test="totalMoney != null">total_money = #{totalMoney},</if>
            <if test="totalTax != null">total_tax = #{totalTax},</if>
            <if test="invoiceTitleContent != null">invoice_title_content = #{invoiceTitleContent},</if>
            <if test="unitDutyParagraph != null">unit_duty_paragraph = #{unitDutyParagraph},</if>
            <if test="registerAddress != null">register_address = #{registerAddress},</if>
            <if test="registerPhone != null">register_phone = #{registerPhone},</if>
            <if test="depositBank != null">deposit_bank = #{depositBank},</if>
            <if test="bankAccount != null">bank_account = #{bankAccount},</if>
            <if test="notifyMobileNo != null">notify_mobile_no = #{notifyMobileNo},</if>
            <if test="notifyEmail != null">notify_email = #{notifyEmail},</if>
            <if test="pdfUrl != null">pdf_url = #{pdfUrl},</if>
            <if test="pdfPreviewUrl != null">pdf_preview_url = #{pdfPreviewUrl},</if>
            <if test="tradeId != null">trade_id = #{tradeId},</if>
            <if test="orderDate != null">order_date = #{orderDate},</if>
            <if test="status != null">status = #{status},</if>
            <if test="mid != null">mid = #{mid},</if>
            <if test="tid != null">tid = #{tid},</if>
            <if test="reverseDate != null">reverse_date = #{reverseDate},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="reopenSign != null">reopen_sign = #{reopenSign},</if>
            <if test="oldTradeId != null">old_trade_id = #{oldTradeId},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteMiniInvoiceRecordById" parameterType="Long">
        delete from mini_invoice_record where id = #{id}
    </delete>

    <delete id="deleteMiniInvoiceRecordByIds" parameterType="String">
        delete from mini_invoice_record where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
