/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* uni.scss */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.agreement-container.data-v-66212410 {
  min-height: 100vh;
  background-color: #f8f9fa;
}

/* 自定义导航栏样式 */
.custom-navbar.data-v-66212410 {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background-color: #fff;
  border-bottom: 1rpx solid #e5e5e5;
  padding-top: var(--status-bar-height);
}
.navbar-content.data-v-66212410 {
  display: flex;
  align-items: center;
  height: 88rpx;
  padding: 0 32rpx;
}
.navbar-left.data-v-66212410 {
  width: 80rpx;
  height: 88rpx;
  display: flex;
  align-items: center;
  justify-content: flex-start;
}
.back-icon.data-v-66212410 {
  font-size: 48rpx;
  color: #333;
  font-weight: 300;
}
.navbar-title.data-v-66212410 {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 88rpx;
  padding-bottom: 16rpx;
  /* 增加标题下方边距 */
}
.title-text.data-v-66212410 {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
}
.navbar-right.data-v-66212410 {
  width: 80rpx;
}
.content-wrapper.data-v-66212410 {
  padding: 32rpx;
  margin-top: calc(var(--status-bar-height) + 88rpx);
}
.loading-container.data-v-66212410,
.error-container.data-v-66212410 {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 0;
}
.loading-text.data-v-66212410 {
  font-size: 28rpx;
  color: #999;
}
.error-text.data-v-66212410 {
  font-size: 28rpx;
  color: #f56c6c;
  margin-bottom: 32rpx;
}
.retry-btn.data-v-66212410 {
  padding: 16rpx 32rpx;
  background-color: #409eff;
  color: #fff;
  border-radius: 8rpx;
  font-size: 28rpx;
  border: none;
}
.agreement-content.data-v-66212410 {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 32rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}
.agreement-body.data-v-66212410 {
  line-height: 1.8;
  color: #666;
  font-size: 28rpx;
}

/* rich-text 内容样式 */
.agreement-body.data-v-66212410 p {
  margin: 16rpx 0;
  line-height: 1.8;
}
.agreement-body.data-v-66212410 strong {
  font-weight: 600;
  color: #333;
}
.agreement-body.data-v-66212410 h1,
.agreement-body.data-v-66212410 h2,
.agreement-body.data-v-66212410 h3 {
  font-weight: 600;
  color: #333;
  margin: 32rpx 0 32rpx 0;
}
.agreement-body.data-v-66212410 h1 {
  font-size: 36rpx;
}
.agreement-body.data-v-66212410 h2 {
  font-size: 32rpx;
}
.agreement-body.data-v-66212410 h3 {
  font-size: 30rpx;
}