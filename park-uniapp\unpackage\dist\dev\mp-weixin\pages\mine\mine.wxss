/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* uni.scss */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.mine-container.data-v-7c2ebfa5 {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding: 24rpx 24rpx;
}
.user-info-section.data-v-7c2ebfa5 {
  border-radius: 24rpx;
  background-color: #fff;
  padding: 28rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 0 10rpx 0 rgba(0, 0, 0, 0.1);
}
.user-info-section .user-info.data-v-7c2ebfa5 {
  display: flex;
  align-items: center;
  padding: 20rpx 0;
}
.user-info-section .user-info .avatar.data-v-7c2ebfa5 {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  border: 4rpx solid #fff;
  margin-right: 24rpx;
}
.user-info-section .user-info .user-detail .nickname.data-v-7c2ebfa5 {
  font-size: 36rpx;
  color: #000000;
  margin-bottom: 12rpx;
}
.user-info-section .user-info .user-detail .user-tags.data-v-7c2ebfa5 {
  padding-top: 10rpx;
  display: flex;
}
.user-info-section .user-info .user-detail .user-tags .tag.data-v-7c2ebfa5 {
  padding: 6rpx 16rpx;
  border-radius: 24rpx;
  font-size: 24rpx;
}
.user-info-section .user-info .user-detail .user-tags .tag.tag-normal.data-v-7c2ebfa5 {
  background-color: #E3F2FD;
  color: #1976D2;
}
.user-info-section .user-info .user-detail .user-tags .tag.tag-enterprise.data-v-7c2ebfa5 {
  background-color: #FFF3E0;
  color: #F57C00;
}
.user-info-section .user-info .user-detail .user-tags .tag.tag-vip.data-v-7c2ebfa5 {
  background-color: #FFF8E1;
  color: #F9A825;
}
.user-info-section .user-info .user-detail .user-tags .tag.tag-unknown.data-v-7c2ebfa5 {
  background-color: #F5F5F5;
  color: #757575;
}
.user-info-section .user-info .arrow-right.data-v-7c2ebfa5 {
  margin-left: auto;
}
.account-card.data-v-7c2ebfa5 {
  background: linear-gradient(90deg, #4BA1FC 0%, #9b8eff 100%);
  border-radius: 24rpx;
  padding: 24rpx;
  color: #fff;
  position: relative;
  z-index: 1;
}
.account-card .card-content.data-v-7c2ebfa5 {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.account-card .card-content .left .title.data-v-7c2ebfa5 {
  font-size: 28rpx;
  margin-bottom: 16rpx;
}
.account-card .card-content .left .amount .symbol.data-v-7c2ebfa5 {
  font-size: 32rpx;
}
.account-card .card-content .left .amount .number.data-v-7c2ebfa5 {
  font-size: 48rpx;
  font-weight: 500;
}
.account-card .card-content .right .view-btn.data-v-7c2ebfa5 {
  background: rgba(255, 255, 255, 0.2);
  padding: 12rpx 32rpx;
  border-radius: 32rpx;
  font-size: 28rpx;
}
.function-list.data-v-7c2ebfa5 {
  background: #fff;
  border-radius: 24rpx;
  padding: 32rpx;
}
.function-list .section-title.data-v-7c2ebfa5 {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 24rpx;
}
.function-list .car-card.data-v-7c2ebfa5 {
  background: #eaf3ff;
  border-radius: 16rpx;
  padding: 24rpx;
  display: flex;
  align-items: center;
  margin-bottom: 32rpx;
}
.function-list .car-card .car-image.data-v-7c2ebfa5 {
  width: 140rpx;
}
.function-list .car-card .car-info.data-v-7c2ebfa5 {
  margin-left: 50rpx;
}
.function-list .car-card .car-info .label.data-v-7c2ebfa5 {
  font-size: 28rpx;
  color: #666666;
  margin-bottom: 12rpx;
}
.function-list .car-card .car-info .plate-number.data-v-7c2ebfa5 {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}
.function-list .car-card .manage-btn.data-v-7c2ebfa5 {
  background: linear-gradient(90deg, #4BA1FC 0%, #8f8fff 100%);
  color: #fff;
  padding: 12rpx 32rpx;
  border-radius: 32rpx;
  font-size: 28rpx;
  margin-left: auto;
}
.function-list .grid-list.data-v-7c2ebfa5 {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 32rpx;
}
.function-list .grid-list .grid-item.data-v-7c2ebfa5 {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12rpx;
  padding: 16rpx 0;
  border-radius: 12rpx;
  transition: all 0.2s ease;
}
.function-list .grid-list .grid-item.data-v-7c2ebfa5:active {
  background-color: #f5f5f5;
}
.function-list .grid-list .grid-item .icon.data-v-7c2ebfa5 {
  width: 64rpx;
  height: 64rpx;
  transition: transform 0.2s ease;
}
.function-list .grid-list .grid-item .name.data-v-7c2ebfa5 {
  font-size: 24rpx;
  color: #333;
  transition: color 0.2s ease;
}