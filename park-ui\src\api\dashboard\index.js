import request from '@/utils/request'

// 获取基础统计数据（新接口）
export function getBasicStatistics() {
  return request({
    url: '/system/dashboard/basic-statistics',
    method: 'get'
  })
}

// 获取所有场库的经纬度信息（新接口）
export function getWarehouseLocations() {
  return request({
    url: '/system/dashboard/warehouse-locations',
    method: 'get'
  })
}

// 获取收入金额和总订单量前五名的场库（新接口）
export function getTopWarehouses(date = null) {
  return request({
    url: '/system/dashboard/top-warehouses',
    method: 'get',
    params: date ? { date } : {}
  })
}

// 获取停车统计数据（首页专用）
export function getHomeStatistics(params = {}) {
  return request({
    url: '/sys/home/<USER>',
    method: 'post',
    data: params
  })
}

// 获取场库统计详情
export function getHomeStatisticsDetail(params = {}) {
  return request({
    url: '/sys/home/<USER>',
    method: 'post',
    data: params
  })
}

// 获取车辆停放次数统计
export function getParkingStatistics(params = {}) {
  return request({
    url: '/sys/home/<USER>',
    method: 'post',
    data: params
  })
}

// 获取数据上报统计
export function getReportStatistics(params = {}) {
  return request({
    url: '/sys/home/<USER>',
    method: 'post',
    data: params
  })
}

// 获取特殊会员统计（按类型）
export function getSpecialUsersStatisticsByType() {
  return request({
    url: '/special/user/statistics/type',
    method: 'get'
  })
}
