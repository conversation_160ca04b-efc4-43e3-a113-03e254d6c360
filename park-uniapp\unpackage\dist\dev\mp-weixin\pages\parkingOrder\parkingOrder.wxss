/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* uni.scss */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.parking-order.data-v-f5c8e10e {
  min-height: 100vh;
  background-color: #f5f5f5;
}
.filter-tabs-fixed.data-v-f5c8e10e {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  background: #ffffff;
  z-index: 10;
  padding: 20rpx 0;
  box-shadow: 0 4rpx 12rpx rgba(36, 107, 253, 0.08);
  display: flex;
  border-radius: 0;
}
.filter-tabs-fixed .filter-tab.data-v-f5c8e10e {
  flex: 1;
  text-align: center;
  padding: 16rpx 12rpx;
  font-size: 26rpx;
  color: #666666;
  border-radius: 12rpx;
  transition: all 0.3s ease;
}
.filter-tabs-fixed .filter-tab.active.data-v-f5c8e10e {
  background: #3b82f6;
  color: white;
  font-weight: 500;
}
.filter-tabs-fixed .filter-tab.data-v-f5c8e10e:not(.active):active {
  background: #f8fafc;
}
.content.data-v-f5c8e10e {
  padding: 120rpx 24rpx 40rpx;
  /* 顶部增加padding避免被固定筛选器遮挡 */
}
.order-list .order-item.data-v-f5c8e10e {
  margin-bottom: 20rpx;
}
.order-list .order-item .order-card.data-v-f5c8e10e {
  background: white;
  border-radius: 20rpx;
  padding: 32rpx;
  box-shadow: 0 8rpx 24rpx rgba(36, 107, 253, 0.1);
}
.order-list .order-item .order-card .card-header.data-v-f5c8e10e {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}
.order-list .order-item .order-card .card-header .order-info .order-no.data-v-f5c8e10e {
  font-size: 20rpx;
  font-weight: bold;
  color: #797979;
}
.order-list .order-item .order-card .card-header .header-right.data-v-f5c8e10e {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
}
.order-list .order-item .order-card .card-header .header-right .status-badge.data-v-f5c8e10e {
  padding: 6rpx 12rpx;
  border-radius: 16rpx;
  font-size: 22rpx;
  font-weight: 500;
}
.order-list .order-item .order-card .card-header .header-right .status-badge.status-progress.data-v-f5c8e10e {
  background: #f0fdf4;
  color: #16a34a;
  border: 1rpx solid #bbf7d0;
}
.order-list .order-item .order-card .card-header .header-right .status-badge.status-failed.data-v-f5c8e10e {
  background: #fef2f2;
  color: #dc2626;
  border: 1rpx solid #fecaca;
}
.order-list .order-item .order-card .card-header .header-right .status-badge.status-cancelled.data-v-f5c8e10e {
  background: #fef3c7;
  color: #d97706;
  border: 1rpx solid #fed7aa;
}
.order-list .order-item .order-card .card-header .header-right .status-badge.status-paid.data-v-f5c8e10e {
  background: #f0f9ff;
  color: #0ea5e9;
  border: 1rpx solid #bae6fd;
}
.order-list .order-item .order-card .card-header .header-right .status-badge.status-unknown.data-v-f5c8e10e {
  background: #f9fafb;
  color: #6b7280;
  border: 1rpx solid #e5e7eb;
}
.order-list .order-item .order-card .card-body .info-item.data-v-f5c8e10e {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}
.order-list .order-item .order-card .card-body .info-item .info-label.data-v-f5c8e10e {
  font-size: 26rpx;
  color: #666666;
  margin-right: 16rpx;
  min-width: 120rpx;
}
.order-list .order-item .order-card .card-body .info-item .info-label.plate-label.data-v-f5c8e10e {
  margin-left: 40rpx;
  min-width: 80rpx;
}
.order-list .order-item .order-card .card-body .info-item .info-value.data-v-f5c8e10e {
  font-size: 26rpx;
  color: #333333;
  flex: 1;
}
.order-list .order-item .order-card .card-body .info-item .info-value.time.data-v-f5c8e10e {
  color: #333333;
}
.order-list .order-item .order-card .card-body .card-bottom.data-v-f5c8e10e {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 10rpx;
  border-top: 1rpx solid #f5f5f5;
}
.order-list .order-item .order-card .card-body .card-bottom .price-info.data-v-f5c8e10e {
  display: flex;
  align-items: center;
}
.order-list .order-item .order-card .card-body .card-bottom .price-info .price-item.data-v-f5c8e10e {
  display: flex;
  align-items: center;
  margin-right: 20rpx;
}
.order-list .order-item .order-card .card-body .card-bottom .price-info .price-item .price-label.data-v-f5c8e10e {
  font-size: 22rpx;
  color: #666666;
  margin-right: 8rpx;
}
.order-list .order-item .order-card .card-body .card-bottom .price-info .price-item .price-value.data-v-f5c8e10e {
  font-size: 26rpx;
  font-weight: bold;
}
.order-list .order-item .order-card .card-body .card-bottom .price-info .price-item .price-value.discount.data-v-f5c8e10e {
  color: #16a34a;
}
.order-list .order-item .order-card .card-body .card-bottom .price-info .price-item .price-value.actual.data-v-f5c8e10e {
  color: #ff0000;
  font-size: 30rpx;
}
.order-list .order-item .order-card .card-body .card-bottom .invoice-actions.data-v-f5c8e10e {
  display: flex;
  align-items: center;
}
.order-list .order-item .order-card .card-body .card-bottom .invoice-actions .invoice-btn.data-v-f5c8e10e {
  display: flex;
  align-items: center;
  padding: 12rpx 16rpx;
  border-radius: 8rpx;
  font-size: 24rpx;
  font-weight: 500;
  margin-left: 10rpx;
}
.order-list .order-item .order-card .card-body .card-bottom .invoice-actions .invoice-btn text.data-v-f5c8e10e {
  margin-left: 6rpx;
}
.order-list .order-item .order-card .card-body .card-bottom .invoice-actions .invoice-btn.send-btn.data-v-f5c8e10e {
  background: #f0f9ff;
  color: #3b82f6;
  border: 1rpx solid #bae6fd;
}
.order-list .order-item .order-card .card-body .card-bottom .invoice-actions .invoice-btn.open-btn.data-v-f5c8e10e {
  background: #3b82f6;
  color: #ffffff;
}
.order-list .order-item .order-card .card-body .card-bottom .invoice-actions .invoice-btn.reopen-btn.data-v-f5c8e10e {
  background: #f59e0b;
  color: #ffffff;
}
.order-list .order-item .order-card .card-body .card-bottom .invoice-actions .invoice-btn.pay-btn.data-v-f5c8e10e {
  background: #16a34a;
  color: #ffffff;
}
.empty-state.data-v-f5c8e10e {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 160rpx 40rpx;
  margin-top: 60rpx;
}
.empty-state .empty-text.data-v-f5c8e10e {
  font-size: 28rpx;
  color: #64748b;
  font-weight: 500;
  margin-top: 32rpx;
}
.popup-cell.data-v-f5c8e10e {
  width: 600rpx;
  text-align: center;
  padding: 44rpx 0;
}
.popup-cell .email.data-v-f5c8e10e {
  padding: 32rpx;
  background: #ffffff;
  border-radius: 20rpx;
}
.popup-cell .email .content_item.data-v-f5c8e10e {
  margin-bottom: 20rpx;
}
.popup-cell .email .content_item .email-title.data-v-f5c8e10e {
  font-size: 28rpx;
  color: #212121;
  font-weight: 500;
  margin-bottom: 16rpx;
  text-align: left;
}
.popup-cell .email .desc.data-v-f5c8e10e {
  font-size: 24rpx;
  color: #f5820e;
  line-height: 1.5;
  margin-top: 16rpx;
}
.popup-cell .choose_btn.data-v-f5c8e10e {
  display: flex;
  justify-content: space-around;
  padding: 0 20rpx;
  margin-top: 32rpx;
}
.popup-cell .choose_btn .cancel_btn.data-v-f5c8e10e, .popup-cell .choose_btn .sure_btn.data-v-f5c8e10e {
  width: 240rpx;
  height: 80rpx;
  line-height: 80rpx;
  border-radius: 80rpx;
  text-align: center;
  font-size: 28rpx;
  font-weight: 600;
}
.popup-cell .choose_btn .cancel_btn.data-v-f5c8e10e {
  background: #ffffff;
  border: 2rpx solid #246bfd;
  color: #246bfd;
}
.popup-cell .choose_btn .sure_btn.data-v-f5c8e10e {
  background: #246bfd;
  color: #ffffff;
}