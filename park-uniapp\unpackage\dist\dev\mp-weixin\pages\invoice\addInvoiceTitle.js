"use strict";
const common_vendor = require("../../common/vendor.js");
const api_invoice = require("../../api/invoice.js");
const _sfc_main = {
  __name: "addInvoiceTitle",
  setup(__props) {
    const isEdit = common_vendor.ref(false);
    const formData = common_vendor.reactive({
      id: null,
      invoiceType: 0,
      // 默认普通发票
      titleType: 0,
      // 默认个人
      invoiceTitleContent: "",
      unitDutyParagraph: "",
      registerAddress: "",
      registerPhone: "",
      depositBank: "",
      bankAccount: ""
    });
    const tempUnitData = common_vendor.reactive({
      unitDutyParagraph: "",
      registerAddress: "",
      registerPhone: "",
      depositBank: "",
      bankAccount: ""
    });
    common_vendor.onLoad((options) => {
      if (options.isEdit === "true") {
        isEdit.value = true;
        if (options.obj) {
          const obj = JSON.parse(decodeURIComponent(options.obj));
          Object.assign(formData, obj);
          if (formData.invoiceType === 1) {
            formData.titleType = 1;
          }
          if (formData.titleType === 1 || formData.invoiceType === 1) {
            Object.assign(tempUnitData, {
              unitDutyParagraph: formData.unitDutyParagraph,
              registerAddress: formData.registerAddress,
              registerPhone: formData.registerPhone,
              depositBank: formData.depositBank,
              bankAccount: formData.bankAccount
            });
          }
        }
      }
    });
    const setInvoiceType = (type) => {
      if (formData.titleType === 1 || formData.invoiceType === 1) {
        Object.assign(tempUnitData, {
          unitDutyParagraph: formData.unitDutyParagraph,
          registerAddress: formData.registerAddress,
          registerPhone: formData.registerPhone,
          depositBank: formData.depositBank,
          bankAccount: formData.bankAccount
        });
      }
      formData.invoiceType = type;
      if (type === 1) {
        formData.titleType = 1;
        Object.assign(formData, tempUnitData);
      } else {
        formData.titleType = 0;
        formData.unitDutyParagraph = "";
        formData.registerAddress = "";
        formData.registerPhone = "";
        formData.depositBank = "";
        formData.bankAccount = "";
      }
    };
    const setTitleType = (type) => {
      if (formData.titleType === 1) {
        Object.assign(tempUnitData, {
          unitDutyParagraph: formData.unitDutyParagraph,
          registerAddress: formData.registerAddress,
          registerPhone: formData.registerPhone,
          depositBank: formData.depositBank,
          bankAccount: formData.bankAccount
        });
      }
      formData.titleType = type;
      if (type === 0) {
        formData.unitDutyParagraph = "";
        formData.registerAddress = "";
        formData.registerPhone = "";
        formData.depositBank = "";
        formData.bankAccount = "";
      } else {
        Object.assign(formData, tempUnitData);
      }
    };
    const handleSubmit = async () => {
      if (!formData.invoiceTitleContent.trim()) {
        common_vendor.index.showToast({
          title: formData.invoiceType === 1 || formData.titleType === 1 ? "请输入单位名称" : "请输入发票抬头",
          icon: "none"
        });
        return;
      }
      if ((formData.invoiceType === 1 || formData.titleType === 1) && !formData.unitDutyParagraph.trim()) {
        common_vendor.index.showToast({
          title: "请输入单位税号",
          icon: "none"
        });
        return;
      }
      common_vendor.index.showLoading({
        title: isEdit.value ? "保存中..." : "添加中...",
        mask: true
      });
      try {
        const apiCall = isEdit.value ? api_invoice.editInvoiceTitle : api_invoice.addInvoiceTitle;
        const res = await apiCall(formData);
        common_vendor.index.hideLoading();
        if (res.code === 200) {
          common_vendor.index.showToast({
            title: isEdit.value ? "保存成功" : "添加成功",
            icon: "success",
            duration: 1500
          });
          setTimeout(() => {
            common_vendor.index.navigateBack();
          }, 1500);
        } else {
          common_vendor.index.showToast({
            title: res.msg || (isEdit.value ? "保存失败" : "添加失败"),
            icon: "none",
            duration: 3e3
          });
        }
      } catch (error) {
        console.error("操作失败:", error);
        common_vendor.index.hideLoading();
        const errorMsg = error.msg || error.message || (isEdit.value ? "保存失败，请重试" : "添加失败，请重试");
        common_vendor.index.showToast({
          title: errorMsg,
          icon: "none",
          duration: 3e3
        });
      }
    };
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: formData.invoiceType === 0 ? 1 : "",
        b: common_vendor.o(($event) => setInvoiceType(0)),
        c: formData.invoiceType === 1 ? 1 : "",
        d: common_vendor.o(($event) => setInvoiceType(1)),
        e: formData.invoiceType === 0
      }, formData.invoiceType === 0 ? {
        f: formData.titleType === 0 ? 1 : "",
        g: common_vendor.o(($event) => setTitleType(0)),
        h: formData.titleType === 1 ? 1 : "",
        i: common_vendor.o(($event) => setTitleType(1))
      } : {}, {
        j: formData.invoiceType === 1 || formData.titleType === 1 ? "请输入发票抬头" : "请输入发票抬头",
        k: formData.invoiceTitleContent,
        l: common_vendor.o(($event) => formData.invoiceTitleContent = $event.detail.value),
        m: formData.invoiceType === 1 || formData.titleType === 1
      }, formData.invoiceType === 1 || formData.titleType === 1 ? {
        n: formData.unitDutyParagraph,
        o: common_vendor.o(($event) => formData.unitDutyParagraph = $event.detail.value),
        p: formData.registerAddress,
        q: common_vendor.o(($event) => formData.registerAddress = $event.detail.value),
        r: formData.registerPhone,
        s: common_vendor.o(($event) => formData.registerPhone = $event.detail.value),
        t: formData.depositBank,
        v: common_vendor.o(($event) => formData.depositBank = $event.detail.value),
        w: formData.bankAccount,
        x: common_vendor.o(($event) => formData.bankAccount = $event.detail.value)
      } : {}, {
        y: common_vendor.t(isEdit.value ? "保存" : "添加"),
        z: common_vendor.o(handleSubmit)
      });
    };
  }
};
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-6a82be67"]]);
wx.createPage(MiniProgramPage);
