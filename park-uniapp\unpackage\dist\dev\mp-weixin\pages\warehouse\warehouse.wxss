/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* uni.scss */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.warehouse-container.data-v-41554ef3 {
  height: 100vh;
  background-color: #f5f5f5;
  position: relative;
}
.tab-header.data-v-41554ef3 {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: linear-gradient(135deg, #4BA1FC, #7e6dff);
  padding: 10rpx 30rpx;
  z-index: 1000;
  flex-shrink: 0;
}
.tab-header .tab-item.data-v-41554ef3 {
  flex: 1;
  text-align: center;
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.9);
  padding: 20rpx 0;
  position: relative;
}
.tab-header .tab-item.active.data-v-41554ef3 {
  color: #ffffff;
  font-weight: bold;
}
.tab-header .tab-item.active.data-v-41554ef3::after {
  content: "";
  position: absolute;
  bottom: 8rpx;
  left: 50%;
  transform: translateX(-50%);
  width: 40rpx;
  height: 4rpx;
  background-color: #ffffff;
  border-radius: 2rpx;
}
.tab-divider.data-v-41554ef3 {
  width: 2rpx;
  height: 40rpx;
  background-color: rgba(255, 255, 255, 0.3);
  margin: 0 20rpx;
}
.warehouse-list-container.data-v-41554ef3 {
  flex: 1;
  overflow: hidden;
  margin-top: 100rpx;
}
.warehouse-list.data-v-41554ef3 {
  height: 100%;
}
.list-content.data-v-41554ef3 {
  padding: 20rpx;
  padding-bottom: 190rpx;
}
.loading-container.data-v-41554ef3 {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400rpx;
  padding: 100rpx 0;
}
.warehouse-item.data-v-41554ef3 {
  background-color: #fff;
  border-radius: 12rpx;
  padding: 20rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 0 10rpx 0 rgba(0, 0, 0, 0.1);
}
.warehouse-item .item-layout.data-v-41554ef3 {
  display: flex;
  align-items: flex-start;
  gap: 20rpx;
}
.warehouse-item .item-layout .item-image.data-v-41554ef3 {
  width: 200rpx;
  height: 160rpx;
  border-radius: 12rpx;
  overflow: hidden;
  flex-shrink: 0;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}
.warehouse-item .item-layout .item-image .image.data-v-41554ef3 {
  width: 100%;
  height: 100%;
}
.warehouse-item .item-layout .item-info.data-v-41554ef3 {
  flex: 1;
  display: flex;
  flex-direction: column;
}
.warehouse-item .item-layout .item-info .name.data-v-41554ef3 {
  font-size: 30rpx;
  font-weight: 600;
  color: #333;
}
.warehouse-item .item-layout .item-info .time.data-v-41554ef3 {
  font-size: 26rpx;
  color: #666;
  margin: 22rpx 0;
}
.warehouse-item .item-layout .item-info .address.data-v-41554ef3 {
  font-size: 24rpx;
  color: #333;
  word-break: break-all;
}
.warehouse-item .item-layout .item-actions.data-v-41554ef3 {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12rpx;
  flex-shrink: 0;
}
.warehouse-item .item-layout .item-actions .distance-info.data-v-41554ef3 {
  margin-bottom: 30rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  height: 68rpx;
  justify-content: center;
}
.warehouse-item .item-layout .item-actions .distance-info.distance-hidden.data-v-41554ef3 {
  visibility: hidden;
  pointer-events: none;
}
.warehouse-item .item-layout .item-actions .distance-info .distance-icon.data-v-41554ef3 {
  width: 40rpx;
  height: 40rpx;
  margin-bottom: 4rpx;
}
.warehouse-item .item-layout .item-actions .distance-info .distance-text.data-v-41554ef3 {
  font-size: 20rpx;
  color: #4BA1FC;
  font-weight: 500;
  min-height: 24rpx;
  line-height: 24rpx;
  display: block;
}
.warehouse-item .item-layout .item-actions .consult-btn.data-v-41554ef3 {
  display: flex;
  align-items: center;
  background: linear-gradient(135deg, #4BA1FC, #7e6dff);
  border-radius: 20rpx;
  padding: 8rpx 16rpx;
}
.warehouse-item .item-layout .item-actions .consult-btn .consult-text.data-v-41554ef3 {
  color: #ffffff;
  font-size: 22rpx;
  font-weight: 500;
  margin-left: 6rpx;
}
.empty-tip.data-v-41554ef3 {
  text-align: center;
  color: #999;
  font-size: 28rpx;
  padding: 100rpx 0;
}