"use strict";
const common_vendor = require("../../common/vendor.js");
const common_assets = require("../../common/assets.js");
const _sfc_main = {
  __name: "index",
  setup(__props) {
    common_vendor.onLoad(() => {
      console.log("占位版本加载");
      setTimeout(() => {
        common_vendor.index.showModal({
          title: "系统升级通知",
          content: "小程序正在进行重大升级，将为您带来全新的停车缴费体验！感谢您的耐心等待。",
          showCancel: false,
          confirmText: "期待新版本",
          success: () => {
            console.log("用户确认升级通知");
          }
        });
      }, 1500);
    });
    common_vendor.onShow(() => {
      console.log("维护页面显示");
    });
    const handleMainFunction = () => {
      common_vendor.index.showToast({
        title: "功能升级中，敬请期待",
        icon: "none",
        duration: 2e3
      });
    };
    const handleContact = () => {
      common_vendor.index.showToast({
        title: "功能开发中，敬请期待",
        icon: "none",
        duration: 2e3
      });
    };
    return (_ctx, _cache) => {
      return {
        a: common_assets._imports_0$8,
        b: common_vendor.o(handleMainFunction),
        c: common_vendor.o(handleContact)
      };
    };
  }
};
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-1cf27b2a"]]);
wx.createPage(MiniProgramPage);
