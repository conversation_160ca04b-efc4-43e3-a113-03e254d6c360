/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* uni.scss */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.package-buy.data-v-e922d8e0 {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding: 20rpx;
}
.notice-card.data-v-e922d8e0 {
  background: linear-gradient(135deg, #fff3e0 0%, #ffeacb 100%);
  border-radius: 16rpx;
  padding: 24rpx;
  margin-bottom: 20rpx;
}
.notice-card .notice-content.data-v-e922d8e0 {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12rpx;
}
.notice-card .notice-content .notice-text.data-v-e922d8e0 {
  font-size: 26rpx;
  color: #e65100;
  line-height: 1.5;
  flex: 1;
}
.cell.data-v-e922d8e0 {
  background-color: #fff;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}
.content.data-v-e922d8e0 {
  margin-bottom: 40rpx;
}
.content_item.data-v-e922d8e0 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
  transition: all 0.2s ease;
}
.title.data-v-e922d8e0 {
  display: flex;
  align-items: center;
  font-size: 28rpx;
  color: #333;
}
.title image.data-v-e922d8e0 {
  width: 32rpx;
  height: 32rpx;
  margin-right: 15rpx;
}
.word.data-v-e922d8e0 {
  font-size: 28rpx;
  color: #666;
}
.word.red.data-v-e922d8e0 {
  color: #ff4757;
  font-weight: bold;
}
.word.money.data-v-e922d8e0 {
  font-size: 32rpx;
  font-weight: bold;
  color: #ff4757;
}
.word.clickable-text.data-v-e922d8e0 {
  color: #4BA1FC;
  font-weight: 500;
  text-decoration: underline;
  -webkit-text-decoration-color: rgba(75, 161, 252, 0.3);
          text-decoration-color: rgba(75, 161, 252, 0.3);
  text-underline-offset: 4rpx;
}
.word .picker-display.data-v-e922d8e0 {
  width: 100%;
  text-align: right;
  color: inherit;
}
.word.dikou.data-v-e922d8e0 {
  display: flex;
  align-items: center;
  color: #ff6b35;
}
.word.dikou .desc.data-v-e922d8e0 {
  font-size: 24rpx;
  margin-right: 10rpx;
}
.word.dikou image.data-v-e922d8e0 {
  width: 24rpx;
  height: 24rpx;
  margin-left: 10rpx;
}
.word .disabled-tip.data-v-e922d8e0 {
  font-size: 22rpx;
  color: #999;
  margin-left: 10rpx;
}
.tips.data-v-e922d8e0 {
  font-size: 24rpx;
}
.tips.red.data-v-e922d8e0 {
  color: #ff4757;
}
.tips.orange.data-v-e922d8e0 {
  color: #ff6b35;
}
.btn.data-v-e922d8e0 {
  width: 100%;
  background: linear-gradient(90deg, #4BA1FC 0%, #7e6dff 100%);
  color: #fff;
  border-radius: 44rpx;
  font-size: 32rpx;
  font-weight: bold;
  border: none;
  box-shadow: 0 8rpx 20rpx rgba(102, 126, 234, 0.3);
}
.btn.data-v-e922d8e0:active {
  transform: translateY(1rpx);
  box-shadow: 0 4rpx 10rpx rgba(102, 126, 234, 0.3);
}
.popup-content.data-v-e922d8e0 {
  background-color: #fff;
  border-radius: 20rpx;
  padding: 60rpx 40rpx 40rpx;
  width: 600rpx;
  text-align: center;
}
.popup-title.data-v-e922d8e0 {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 30rpx;
}
.popup-desc.data-v-e922d8e0 {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 50rpx;
}
.popup-buttons.data-v-e922d8e0 {
  display: flex;
  gap: 30rpx;
}
.popup-btn.data-v-e922d8e0 {
  flex: 1;
  height: 80rpx;
  border-radius: 40rpx;
  font-size: 28rpx;
  border: none;
}
.popup-btn.cancel.data-v-e922d8e0 {
  background-color: #f5f5f5;
  color: #666;
}
.popup-btn.confirm.data-v-e922d8e0 {
  background: linear-gradient(90deg, #4BA1FC 0%, #7e6dff 100%);
  color: #fff;
}