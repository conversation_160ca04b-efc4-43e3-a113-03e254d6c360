/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* uni.scss */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.package-container.data-v-9a861780 {
  background: linear-gradient(180deg, #eaf6ff 0%, #e3edff 100%);
}
.tab-header.data-v-9a861780 {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: linear-gradient(135deg, #4BA1FC, #7e6dff);
  padding: 10rpx 30rpx;
  z-index: 10;
  flex-shrink: 0;
}
.tab-item.data-v-9a861780 {
  flex: 1;
  text-align: center;
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.9);
  padding: 20rpx 0;
  position: relative;
}
.tab-item .tab-text.data-v-9a861780 {
  margin-right: 0;
}
.tab-item.active.data-v-9a861780 {
  color: #ffffff;
  font-weight: bold;
}
.tab-item.active.data-v-9a861780::after {
  content: "";
  position: absolute;
  bottom: 8rpx;
  left: 50%;
  transform: translateX(-50%);
  width: 40rpx;
  height: 4rpx;
  background-color: #ffffff;
  border-radius: 2rpx;
}
.tab-divider.data-v-9a861780 {
  width: 2rpx;
  height: 40rpx;
  background-color: rgba(255, 255, 255, 0.7);
  margin: 0 20rpx;
}
.sub-type-container.data-v-9a861780 {
  display: flex;
  padding: 20rpx 40rpx;
  gap: 20rpx;
}
.sub-type-item.data-v-9a861780 {
  padding: 14rpx 30rpx;
  text-align: center;
  background-color: #fff;
  font-size: 28rpx;
  color: #666;
  border-radius: 40rpx;
}
.sub-type-item.active.data-v-9a861780 {
  background: linear-gradient(135deg, #4BA1FC, #6B73FF);
  color: #fff;
  font-weight: bold;
}