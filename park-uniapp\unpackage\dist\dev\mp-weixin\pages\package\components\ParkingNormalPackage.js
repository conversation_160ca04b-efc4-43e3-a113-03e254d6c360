"use strict";
const common_vendor = require("../../../common/vendor.js");
const api_package = require("../../../api/package.js");
const api_warehouse = require("../../../api/warehouse.js");
if (!Array) {
  const _easycom_up_icon2 = common_vendor.resolveComponent("up-icon");
  const _easycom_up_empty2 = common_vendor.resolveComponent("up-empty");
  (_easycom_up_icon2 + _easycom_up_empty2)();
}
const _easycom_up_icon = () => "../../../node-modules/uview-plus/components/u-icon/u-icon.js";
const _easycom_up_empty = () => "../../../node-modules/uview-plus/components/u-empty/u-empty.js";
if (!Math) {
  (_easycom_up_icon + _easycom_up_empty + WarehouseSelector + CarSelector)();
}
const WarehouseSelector = () => "../../../components/warehouse-selector/warehouse-selector.js";
const CarSelector = () => "../../../components/car-selector/car-selector.js";
const _sfc_main = {
  __name: "ParkingNormalPackage",
  setup(__props, { expose: __expose }) {
    const vipType = common_vendor.ref(0);
    const wareHouseList = common_vendor.ref([]);
    const wareHouseSelector = common_vendor.ref(false);
    const currentWarehouse = common_vendor.ref({ id: 0, name: "选择场库" });
    const packageList = common_vendor.ref([]);
    const choosePackage = common_vendor.ref({});
    const carList = common_vendor.ref([]);
    const selectedCar = common_vendor.ref({});
    const selectedCars = common_vendor.ref([]);
    const showCarSelector = common_vendor.ref(false);
    const userPackagePlate = common_vendor.ref({});
    const userPackagePlateList = common_vendor.ref([]);
    const initData = async () => {
      await initWarehouseData();
      await initPackageData();
      await initUserPackagePlateList();
    };
    const initWarehouseData = async () => {
      const res = await api_warehouse.getParkWareHouseList();
      wareHouseList.value = res.data.map((item) => ({
        id: item.id,
        name: item.warehouseName,
        latitude: item.latitude,
        longitude: item.longitude,
        parkingSpaceType: item.parkingSpaceType
      }));
      const cachedWarehouse = common_vendor.index.getStorageSync("currentWarehouse");
      if (cachedWarehouse && wareHouseList.value.some((w) => w.id === cachedWarehouse.id)) {
        currentWarehouse.value = cachedWarehouse;
      } else if (wareHouseList.value.length > 0) {
        currentWarehouse.value = wareHouseList.value[0];
        common_vendor.index.setStorageSync("currentWarehouse", currentWarehouse.value);
      }
    };
    const initPackageData = async () => {
      try {
        if (!currentWarehouse.value || !currentWarehouse.value.id) {
          packageList.value = [];
          choosePackage.value = {};
          return;
        }
        const warehouseId = currentWarehouse.value.id;
        const res = await api_package.getPackageList({ warehouseId });
        packageList.value = res.data || [];
        if (packageList.value.length > 0) {
          choosePackage.value = packageList.value[0];
        }
      } catch (error) {
        common_vendor.index.showToast({
          title: "套餐数据加载失败",
          icon: "none"
        });
        packageList.value = [];
        choosePackage.value = {};
      }
    };
    const initUserPackagePlateList = async () => {
      try {
        if (!currentWarehouse.value || !currentWarehouse.value.id) {
          userPackagePlateList.value = [];
          carList.value = [];
          selectedCar.value = {};
          selectedCars.value = [];
          userPackagePlate.value = {};
          return;
        }
        const res = await api_package.getUserPackagePlateList({
          warehouseId: currentWarehouse.value.id,
          vipType: vipType.value
        });
        userPackagePlateList.value = res.data || [];
        console.log("userPackagePlateList", userPackagePlateList.value);
        if (userPackagePlateList.value.length === 0) {
          carList.value = [];
          selectedCar.value = {};
          selectedCars.value = [];
          userPackagePlate.value = {};
          return;
        }
        carList.value = userPackagePlateList.value.map((item) => ({
          plateNo: item.plateNo || "",
          ...item
          // 保留其他套餐信息
        }));
        if (carList.value.length > 0) {
          selectedCar.value = carList.value[0];
          selectedCars.value = [selectedCar.value];
          userPackagePlate.value = selectedCar.value;
          try {
            common_vendor.index.setStorageSync("selectedCar", selectedCar.value);
          } catch (error) {
            console.error("缓存车辆信息失败:", error);
          }
        }
      } catch (error) {
        console.error("获取用户套餐列表失败:", error);
        userPackagePlateList.value = [];
        carList.value = [];
        selectedCar.value = {};
        selectedCars.value = [];
        userPackagePlate.value = {};
        common_vendor.index.showToast({
          title: "套餐数据加载失败",
          icon: "none"
        });
      }
    };
    const showWarehouseSelector = () => {
      wareHouseSelector.value = true;
    };
    const closeWarehouseSelector = () => {
      wareHouseSelector.value = false;
    };
    const selectWarehouse = (warehouse) => {
      currentWarehouse.value = warehouse;
      common_vendor.index.setStorageSync("currentWarehouse", warehouse);
      closeWarehouseSelector();
      initPackageData();
      initUserPackagePlateList();
    };
    const openCarSelector = () => {
      if (!hasValidPackageCar()) {
        return;
      }
      showCarSelector.value = true;
    };
    const hasValidPackageCar = () => {
      if (carList.value.length === 0) {
        common_vendor.index.showToast({
          title: "暂无有效套餐车辆",
          icon: "none",
          duration: 1500
        });
        return false;
      }
      return true;
    };
    const onCarSelectionConfirm = (selectedCarList) => {
      if (selectedCarList.length > 0) {
        selectedCar.value = selectedCarList[0];
        userPackagePlate.value = selectedCar.value;
        try {
          common_vendor.index.setStorageSync("selectedCar", selectedCar.value);
        } catch (error) {
          console.error("缓存车辆信息失败:", error);
        }
      }
    };
    const handleChoosePackage = (item) => {
      choosePackage.value = item;
    };
    const handleBuyPackage = () => {
      if (!choosePackage.value || !choosePackage.value.id) {
        common_vendor.index.showToast({
          title: "请先选择套餐",
          icon: "none"
        });
        return;
      }
      const packageOrder = {
        packageId: choosePackage.value.id,
        packageName: choosePackage.value.packageName,
        packagePrice: choosePackage.value.packagePrice,
        packageDays: choosePackage.value.packageType,
        vipType: vipType.value
      };
      common_vendor.index.navigateTo({ url: "/pages/package/packageBuy?packageOrder=" + JSON.stringify(packageOrder) });
    };
    __expose({
      initData
    });
    return (_ctx, _cache) => {
      var _a, _b, _c, _d;
      return common_vendor.e({
        a: common_vendor.t(currentWarehouse.value.name),
        b: common_vendor.p({
          name: "arrow-right",
          size: "12",
          color: "#fff"
        }),
        c: common_vendor.o(showWarehouseSelector),
        d: common_vendor.t(selectedCar.value.plateNo || (carList.value.length === 0 ? "暂无有效套餐车辆" : "点击选择")),
        e: carList.value.length > 0
      }, carList.value.length > 0 ? {
        f: common_vendor.p({
          name: "arrow-right",
          size: "12",
          color: "#fff"
        })
      } : {}, {
        g: common_vendor.o(openCarSelector),
        h: common_vendor.t(((_a = userPackagePlate.value) == null ? void 0 : _a.beginVipTime) || "--"),
        i: common_vendor.t(((_b = userPackagePlate.value) == null ? void 0 : _b.endVipTime) || "--"),
        j: ((_c = packageList.value) == null ? void 0 : _c.length) > 0
      }, ((_d = packageList.value) == null ? void 0 : _d.length) > 0 ? {
        k: common_vendor.f(packageList.value, (item, k0, i0) => {
          return {
            a: common_vendor.t(item.packageName),
            b: common_vendor.t(item.packagePrice),
            c: item.id,
            d: choosePackage.value.id === item.id ? 1 : "",
            e: common_vendor.o(($event) => handleChoosePackage(item), item.id)
          };
        })
      } : {
        l: common_vendor.p({
          mode: "data",
          text: "暂无可用套餐"
        })
      }, {
        m: common_vendor.t("购买套餐"),
        n: !choosePackage.value.id || packageList.value.length === 0,
        o: common_vendor.o(handleBuyPackage),
        p: common_vendor.o(closeWarehouseSelector),
        q: common_vendor.o(selectWarehouse),
        r: common_vendor.p({
          show: wareHouseSelector.value,
          ["warehouse-list"]: wareHouseList.value,
          ["current-warehouse"]: currentWarehouse.value,
          ["window-height-half"]: 400
        }),
        s: common_vendor.o(($event) => showCarSelector.value = false),
        t: common_vendor.o(onCarSelectionConfirm),
        v: common_vendor.o(($event) => selectedCars.value = $event),
        w: common_vendor.p({
          show: showCarSelector.value,
          ["car-list"]: carList.value,
          ["max-select"]: 1,
          modelValue: selectedCars.value
        })
      });
    };
  }
};
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-f658ade8"]]);
wx.createComponent(Component);
