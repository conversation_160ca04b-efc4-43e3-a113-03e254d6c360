/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* uni.scss */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.invoice-title-container.data-v-59070f7b {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding: 24rpx;
  padding-bottom: 140rpx;
  position: relative;
}
.invoice-title-list .invoice-title-item.data-v-59070f7b {
  margin-bottom: 24rpx;
}
.invoice-title-list .invoice-title-item .item-card.data-v-59070f7b {
  background: #fff;
  border-radius: 24rpx;
  padding: 32rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}
.invoice-title-list .invoice-title-item .item-card .item-header.data-v-59070f7b {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-bottom: 20rpx;
  border-bottom: 2rpx solid rgba(189, 189, 189, 0.2);
}
.invoice-title-list .invoice-title-item .item-card .item-header .item-type.data-v-59070f7b {
  display: flex;
  align-items: center;
}
.invoice-title-list .invoice-title-item .item-card .item-header .item-type .type-icon.data-v-59070f7b {
  margin-right: 8rpx;
}
.invoice-title-list .invoice-title-item .item-card .item-header .item-type .type-text.data-v-59070f7b {
  font-size: 24rpx;
  font-weight: bold;
  color: #616161;
  line-height: 44rpx;
}
.invoice-title-list .invoice-title-item .item-card .item-header .item-actions.data-v-59070f7b {
  display: flex;
  align-items: center;
  gap: 24rpx;
}
.invoice-title-list .invoice-title-item .item-card .item-header .item-actions .action-btn.data-v-59070f7b {
  display: flex;
  align-items: center;
  font-size: 28rpx;
  font-weight: 400;
  color: #9e9e9e;
  line-height: 44rpx;
}
.invoice-title-list .invoice-title-item .item-card .item-header .item-actions .action-btn .action-icon.data-v-59070f7b {
  margin-right: 8rpx;
}
.invoice-title-list .invoice-title-item .item-card .item-content.data-v-59070f7b {
  padding-top: 20rpx;
}
.invoice-title-list .invoice-title-item .item-card .item-content .content-title.data-v-59070f7b {
  font-size: 32rpx;
  font-weight: bold;
  color: #212121;
  padding-bottom: 8rpx;
  line-height: 44rpx;
}
.invoice-title-list .invoice-title-item .item-card .item-content .content-subtitle.data-v-59070f7b {
  font-size: 24rpx;
  font-weight: 400;
  color: #9e9e9e;
  line-height: 44rpx;
}
.empty-state.data-v-59070f7b {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 0;
}

/* 底部固定的添加按钮 */
.bottom-add-btn.data-v-59070f7b {
  position: fixed;
  bottom: 40rpx;
  width: 85%;
  left: 50%;
  transform: translateX(-50%);
  background: linear-gradient(90deg, #4ba1fc 0%, #7e6dff 100%);
  color: #fff;
  padding: 20rpx;
  border-radius: 50rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 999;
}
.bottom-add-btn .add-icon.data-v-59070f7b {
  margin-right: 12rpx;
}
.bottom-add-btn .add-text.data-v-59070f7b {
  font-size: 32rpx;
  font-weight: 500;
}