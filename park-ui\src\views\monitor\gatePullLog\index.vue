<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="品牌名称" prop="parkingName">
        <el-input
          v-model="queryParams.parkingName"
          placeholder="请输入品牌名称"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="请求地址" prop="accessAddress">
        <el-input
          v-model="queryParams.accessAddress"
          placeholder="请输入请求地址"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="方法路径" prop="address">
        <el-input
          v-model="queryParams.address"
          placeholder="请输入方法路径"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="最后更新时间" style="width: 240px">
        <el-date-picker
          v-model="singleDate"
          value-format="YYYY-MM-DD"
          type="date"
          placeholder="选择日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="Delete"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['monitor:gatePullLog:remove']"
        >删除</el-button>
      </el-col>
      <!-- <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="Delete"
          @click="handleClean"
          v-hasPermi="['monitor:gatePullLog:remove']"
        >清空</el-button>
      </el-col> -->
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="Download"
          @click="handleExport"
          v-hasPermi="['monitor:gatePullLog:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="gatePullLogList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="50" align="center" />
      <el-table-column label="品牌名称" align="center" prop="parkingName" />
      <el-table-column label="请求地址" align="center" prop="accessAddress" />
      <el-table-column label="方法路径" align="center" prop="address" />
      <el-table-column label="推送数据" align="center" prop="data" width="300">
        <template #default="scope">
          <div class="data-preview">{{ truncateData(scope.row.data) }}</div>
        </template>
      </el-table-column>
      <el-table-column label="最后更新时间" align="center" prop="lastUpdate" width="180" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-tooltip content="查看" placement="top">
            <el-button link type="primary" icon="View" @click="handleView(scope.row)" v-hasPermi="['monitor:gatePullLog:query']"></el-button>
          </el-tooltip>
          <el-tooltip content="删除" placement="top">
            <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['monitor:gatePullLog:remove']"></el-button>
          </el-tooltip>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 查看道闸推送日志详细 -->
    <el-dialog title="道闸推送日志详细" v-model="open" width="800px" append-to-body>
      <el-form ref="gatePullLogRef" :model="form" label-width="120px" readonly>
        <el-row>
          <el-col :span="12">
            <el-form-item label="日志ID：">{{ form.id }}</el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="品牌名称：">{{ form.parkingName }}</el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="请求地址：">{{ form.accessAddress }}</el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="方法路径：">{{ form.address }}</el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="最后更新时间：">{{ form.lastUpdate }}</el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="推送数据：">
              <div class="json-viewer">
                <pre>{{ formatJsonData(form.data) }}</pre>
              </div>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="cancel">关 闭</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="GatePullLog">
import { listGatePullLog, getGatePullLog, delGatePullLog, cleanGatePullLog } from "@/api/monitor/gatePullLog";

const { proxy } = getCurrentInstance();

const gatePullLogList = ref([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const singleDate = ref('');

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    parkingName: null,
    accessAddress: null,
    address: null,
    data: null,
    lastUpdate: null
  }
});

const { queryParams, form } = toRefs(data);

/** 查询道闸推送日志列表 */
function getList() {
  loading.value = true;
  const params = { ...queryParams.value };
  if (singleDate.value) {
    params.lastUpdate = singleDate.value;
  }
  listGatePullLog(params).then(response => {
    gatePullLogList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
}

// 取消按钮
function cancel() {
  open.value = false;
  reset();
}

// 表单重置
function reset() {
  form.value = {
    id: null,
    parkingName: null,
    accessAddress: null,
    address: null,
    data: null,
    lastUpdate: null
  };
  proxy.resetForm("gatePullLogRef");
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  singleDate.value = '';
  proxy.resetForm("queryRef");
  handleQuery();
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 查看按钮操作 */
function handleView(row) {
  reset();
  const _id = row.id || ids.value;
  getGatePullLog(_id).then(response => {
    form.value = response.data;
    open.value = true;
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  const _ids = row.id || ids.value;
  proxy.$modal.confirm('是否确认删除道闸推送日志编号为"' + _ids + '"的数据项？').then(function() {
    return delGatePullLog(_ids);
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("删除成功");
  }).catch(() => {});
}

/** 清空按钮操作 */
function handleClean() {
  proxy.$modal.confirm('是否确认清空所有道闸推送日志数据项？').then(function() {
    return cleanGatePullLog();
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("清空成功");
  }).catch(() => {});
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download('system/monitor/gatePullLog/export', {
    ...queryParams.value
  }, `gatePullLog_${new Date().getTime()}.xlsx`)
}

/** 格式化JSON数据 */
function formatJsonData(jsonStr) {
  if (!jsonStr) return '';
  try {
    const obj = JSON.parse(jsonStr);
    return JSON.stringify(obj, null, 2);
  } catch (e) {
    return jsonStr;
  }
}

/** 截断推送数据显示 */
function truncateData(data) {
  if (!data) return '';
  // 限制显示长度为50个字符，超出部分用...表示
  return data.length > 50 ? data.substring(0, 50) + '...' : data;
}

getList();
</script>

<style scoped>
.json-viewer {
  background-color: #f5f5f5;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 12px;
  max-height: 400px;
  overflow-y: auto;
}

.json-viewer pre {
  margin: 0;
  font-family: 'Courier New', Courier, monospace;
  font-size: 13px;
  line-height: 1.5;
  color: #303133;
  white-space: pre-wrap;
  word-wrap: break-word;
}

.data-preview {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 100%;
}
</style>
