"use strict";
const common_vendor = require("../../../common/vendor.js");
const common_assets = require("../../../common/assets.js");
const api_package = require("../../../api/package.js");
const _sfc_main = {
  __name: "ParkingVipPackage",
  props: {
    userType: {
      type: String,
      default: "VIP套餐"
      // 默认VIP套餐
    }
  },
  setup(__props, { expose: __expose }) {
    const props = __props;
    const vipUserCarList = common_vendor.ref(null);
    const vipCards = common_vendor.computed(() => {
      const carPackages = vipUserCarList.value || [];
      const cards = [];
      for (let i = 0; i < carPackages.length && i < 3; i++) {
        const car = carPackages[i];
        cards.push({
          ...car,
          warehouseName: car.warehouseName,
          // 判断是否未过期：beginVipTime和endVipTime都为null表示未过期
          isVip: car.beginVipTime !== null && car.endVipTime !== null
        });
      }
      while (cards.length < 3) {
        cards.push({
          plateNo: null,
          warehouseName: null,
          beginVipTime: null,
          endVipTime: null,
          isVip: false
        });
      }
      console.log("cards", cards);
      return cards;
    });
    const currentCarCount = common_vendor.computed(() => {
      const carPackages = vipUserCarList.value || [];
      return carPackages.length;
    });
    const vipType = common_vendor.computed(() => {
      return props.userType === "集团套餐" ? 1 : 2;
    });
    const initData = async () => {
      try {
        common_vendor.index.showLoading({ title: "加载中..." });
        await loadVipUserCarList();
        common_vendor.index.hideLoading();
      } catch (error) {
        common_vendor.index.hideLoading();
        common_vendor.index.showToast({
          title: error.message || "数据加载失败",
          icon: "none"
        });
      }
    };
    const loadVipUserCarList = async () => {
      try {
        const res = await api_package.getVipUserPackageList(vipType.value);
        vipUserCarList.value = res.data;
      } catch (error) {
        throw new Error(error.message || "获取VIP用户数据失败");
      }
    };
    const isButtonClickable = (cardData, index) => {
      if (cardData.plateNo !== null) {
        return true;
      }
      return index <= currentCarCount.value;
    };
    const getButtonText = (cardData, index) => {
      if (cardData.plateNo === null) {
        return "购买";
      }
      if (cardData.isVip) {
        return "续费";
      } else {
        return "购买";
      }
    };
    const handleAction = (cardData, index) => {
      if (!isButtonClickable(cardData, index)) {
        return;
      }
      const isRenewal = cardData.isVip;
      const packageOrder = {
        warehouseId: cardData.warehouseId,
        warehouseName: cardData.warehouseName,
        plateNo: cardData.plateNo,
        index: index + 1,
        //作为索引，判断是第几辆车
        vipType: vipType.value,
        isRenewal,
        beginVipTime: cardData.beginVipTime,
        expirationTime: cardData.endVipTime
      };
      if (vipType.value === 2) {
        packageOrder.packageName = "VIP年度套餐";
        packageOrder.packagePrice = 0.01;
        packageOrder.packageDays = 1;
        packageOrder.packageId = 6;
      } else {
        packageOrder.packageName = null;
        packageOrder.packagePrice = null;
        packageOrder.packageDays = null;
        packageOrder.packageId = null;
      }
      console.log("packageOrder", packageOrder);
      common_vendor.index.navigateTo({
        url: `/pages/package/packageVipBuy?packageOrder=${encodeURIComponent(
          JSON.stringify(packageOrder)
        )}`
      });
    };
    __expose({
      initData
    });
    return (_ctx, _cache) => {
      return {
        a: common_vendor.f(vipCards.value, (cardData, index, i0) => {
          return common_vendor.e({
            a: common_vendor.t(cardData.plateNo || "--"),
            b: common_vendor.t(cardData.warehouseName || "--"),
            c: common_vendor.t(cardData.beginVipTime || "--"),
            d: common_vendor.t(cardData.endVipTime || "--"),
            e: cardData.plateNo
          }, cardData.plateNo ? {
            f: common_assets._imports_0$3
          } : {}, {
            g: common_vendor.t(getButtonText(cardData)),
            h: !isButtonClickable(cardData, index) ? 1 : "",
            i: common_vendor.o(($event) => handleAction(cardData, index), index),
            j: index
          });
        })
      };
    };
  }
};
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-b8425ce1"]]);
wx.createComponent(Component);
