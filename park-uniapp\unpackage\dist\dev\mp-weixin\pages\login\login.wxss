/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* uni.scss */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.login-container.data-v-e4e4508d {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  position: relative;
  background: #f5f9ff;
  overflow: hidden;
}
.custom-navbar.data-v-e4e4508d {
  position: fixed;
  left: 0;
  right: 0;
  z-index: 999;
  display: flex;
  align-items: center;
  padding-left: 15rpx;
}
.custom-navbar .nav-back.data-v-e4e4508d {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(0, 0, 0, 0.2);
  border-radius: 50%;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  transition: all 0.3s ease;
}
.custom-navbar .nav-back.data-v-e4e4508d:active {
  transform: scale(0.95);
  background: rgba(0, 0, 0, 0.3);
}
.login-header.data-v-e4e4508d {
  width: 100%;
  height: 40vh;
  position: relative;
  z-index: 1;
}
.login-header .login-header-image.data-v-e4e4508d {
  width: 100%;
  height: 100%;
  object-fit: cover;
}
.login-content.data-v-e4e4508d {
  position: relative;
  margin-top: -60rpx;
  background: #ffffff;
  border-radius: 40rpx 40rpx 0 0;
  padding: 60rpx 40rpx 40rpx;
  z-index: 2;
  flex: 1;
  box-shadow: 0 -4rpx 16rpx rgba(0, 0, 0, 0.05);
}
.login-form.data-v-e4e4508d {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  z-index: 1;
}
.login-form .login-form-phone.data-v-e4e4508d,
.login-form .login-form-code.data-v-e4e4508d {
  width: 90%;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16rpx;
  padding: 32rpx;
  display: flex;
  align-items: center;
  margin-bottom: 24rpx;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.03);
  border: 1rpx solid rgba(0, 0, 0, 0.08);
}
.login-form .login-form-phone .login-form-text.data-v-e4e4508d,
.login-form .login-form-code .login-form-text.data-v-e4e4508d {
  color: #333333;
  margin-right: 20rpx;
  font-size: 28rpx;
  font-weight: 500;
}
.login-form .login-form-phone .login-form-input.data-v-e4e4508d,
.login-form .login-form-code .login-form-input.data-v-e4e4508d {
  flex: 1;
  font-size: 28rpx;
  color: #333333;
}
.login-form .login-form-phone .login-form-input.data-v-e4e4508d::-webkit-input-placeholder, .login-form .login-form-code .login-form-input.data-v-e4e4508d::-webkit-input-placeholder {
  color: #999999;
}
.login-form .login-form-phone .login-form-input.data-v-e4e4508d::placeholder,
.login-form .login-form-code .login-form-input.data-v-e4e4508d::placeholder {
  color: #999999;
}
.login-form .login-form-code .login-form-code-button.data-v-e4e4508d {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 160rpx;
  height: 56rpx;
  border-radius: 32rpx;
  font-size: 24rpx;
  color: #fff;
  background: linear-gradient(135deg, #60adff, #8677fc);
  box-shadow: 0 4rpx 12rpx rgba(96, 173, 255, 0.2);
  transition: all 0.3s ease;
  font-weight: 500;
  padding: 0;
}
.login-form .login-form-code .login-form-code-button.data-v-e4e4508d:active {
  transform: scale(0.98);
  box-shadow: 0 2rpx 8rpx rgba(96, 173, 255, 0.15);
}
.login-form .login-form-code .login-form-code-button[disabled].data-v-e4e4508d {
  opacity: 0.7;
  background: #cccccc;
}
.agreement.data-v-e4e4508d {
  display: flex;
  align-items: center;
  margin: 30rpx 40rpx;
  z-index: 1;
}
.agreement .checkbox.data-v-e4e4508d {
  width: 36rpx;
  height: 36rpx;
  border: 2rpx solid #ddd;
  border-radius: 50%;
  margin-right: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}
.agreement .checkbox.checked.data-v-e4e4508d {
  background: #60adff;
  border-color: #60adff;
}
.agreement .agreement-text.data-v-e4e4508d {
  font-size: 24rpx;
  color: #666666;
}
.agreement .agreement-link.data-v-e4e4508d {
  font-size: 24rpx;
  color: #60adff;
  margin: 0 4rpx;
}
.agreement .agreement-link.data-v-e4e4508d:active {
  opacity: 0.8;
}
.login-button.data-v-e4e4508d {
  width: 100%;
  margin-top: 40rpx;
  padding: 32rpx 0;
  background: linear-gradient(135deg, #60adff, #8677fc);
  border-radius: 16rpx;
  text-align: center;
  box-shadow: 0 12rpx 24rpx rgba(96, 173, 255, 0.25);
  transition: all 0.3s ease;
  z-index: 1;
  position: relative;
  overflow: hidden;
}
.login-button.data-v-e4e4508d::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  transform: translateX(-100%);
}
.login-button.data-v-e4e4508d:active {
  transform: scale(0.98);
  box-shadow: 0 6rpx 12rpx rgba(96, 173, 255, 0.2);
}
.login-button.data-v-e4e4508d:active::before {
  transform: translateX(100%);
  transition: transform 0.6s ease;
}
.login-button .login-button-text.data-v-e4e4508d {
  color: #fff;
  font-size: 32rpx;
  font-weight: 600;
  letter-spacing: 3rpx;
}
.wechat-login.data-v-e4e4508d {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-top: 60rpx;
  z-index: 1;
  background: none;
  border: none;
  padding: 0;
  line-height: 1;
}
.wechat-login.data-v-e4e4508d::after {
  border: none;
}
.wechat-login .wechat-icon.data-v-e4e4508d {
  width: 88rpx;
  height: 88rpx;
  background-color: #40c27f;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8rpx 16rpx rgba(64, 194, 127, 0.2);
  transition: all 0.3s ease;
}
.wechat-login .wechat-icon.data-v-e4e4508d:active {
  transform: scale(0.95);
  box-shadow: 0 4rpx 8rpx rgba(64, 194, 127, 0.15);
}
.custom-popup.data-v-e4e4508d {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 999;
}
.custom-popup .popup-mask.data-v-e4e4508d {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
}
.custom-popup .popup-content.data-v-e4e4508d {
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ffffff;
  border-radius: 24rpx 24rpx 0 0;
  padding: 40rpx 30rpx;
  transform: translateY(0);
}
.custom-popup .popup-content .popup-header.data-v-e4e4508d {
  text-align: center;
  margin-bottom: 40rpx;
}
.custom-popup .popup-content .popup-header .popup-title.data-v-e4e4508d {
  font-size: 34rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 20rpx;
  display: block;
}
.custom-popup .popup-content .popup-header .popup-desc.data-v-e4e4508d {
  font-size: 28rpx;
  color: #666;
  line-height: 1.5;
}
.custom-popup .popup-content .popup-body.data-v-e4e4508d {
  padding: 0 30rpx;
}
.custom-popup .popup-content .popup-body .phone-number.data-v-e4e4508d {
  background: #ffffff;
  padding: 24rpx;
  border-radius: 12rpx;
  text-align: center;
  margin-bottom: 24rpx;
  font-size: 36rpx;
  color: #333;
  font-weight: 500;
  position: relative;
}
.custom-popup .popup-content .popup-body .phone-number .phone-tip.data-v-e4e4508d {
  font-size: 24rpx;
  color: #666;
  margin-top: 8rpx;
  display: block;
}
.custom-popup .popup-content .popup-body .phone-number.data-v-e4e4508d:active {
  opacity: 0.8;
}
.custom-popup .popup-content .popup-body .cancel-btn.data-v-e4e4508d {
  text-align: center;
  font-size: 32rpx;
  color: #666;
  padding: 20rpx 0;
}
.custom-popup .popup-content .popup-body .cancel-btn.data-v-e4e4508d:active {
  opacity: 0.6;
}