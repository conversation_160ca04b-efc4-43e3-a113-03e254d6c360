/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* uni.scss */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.mine-edit-container.data-v-756671fa {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding: 24rpx;
}
.edit-card.data-v-756671fa {
  background-color: #fff;
  border-radius: 24rpx;
  padding: 20rpx 36rpx 20rpx 28rpx;
  margin-bottom: 24rpx;
}
.edit-item-avatar.data-v-756671fa {
  display: flex;
  align-items: center;
  justify-content: space-between;
  min-height: 120rpx;
  padding: 16rpx 0;
  border-bottom: 2rpx solid rgba(189, 189, 189, 0.2);
}
.edit-item-avatar .edit-label-avatar.data-v-756671fa {
  font-size: 32rpx;
  color: #000000;
}
.edit-item-avatar .avatar-wrapper.data-v-756671fa {
  display: flex;
  align-items: center;
  gap: 12rpx;
}
.edit-item-avatar .avatar-btn.data-v-756671fa {
  width: 120rpx;
  height: 120rpx;
  border: none;
  background: transparent;
  padding: 0;
  margin: 0;
  border-radius: 50%;
  overflow: hidden;
}
.edit-item-avatar .avatar-btn.data-v-756671fa::after {
  border: none;
}
.edit-item-avatar .avatar-image.data-v-756671fa {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  border: 4rpx solid #f0f0f0;
}
.edit-item-horizontal.data-v-756671fa {
  display: flex;
  align-items: center;
  justify-content: space-between;
  min-height: 120rpx;
  padding: 16rpx 0;
  border-bottom: 2rpx solid rgba(189, 189, 189, 0.2);
}
.edit-item-horizontal .edit-label-horizontal.data-v-756671fa {
  font-size: 32rpx;
  color: #000000;
  flex-shrink: 0;
  min-width: 160rpx;
}
.edit-item-horizontal .content-wrapper.data-v-756671fa {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  flex: 1;
  margin-left: 20rpx;
  gap: 12rpx;
}
.edit-item-horizontal .content-text.data-v-756671fa {
  font-size: 32rpx;
  color: #000000;
  text-align: right;
  border: none;
  background: transparent;
  flex: 1;
}
.edit-item-horizontal .content-text.data-v-756671fa::-webkit-input-placeholder {
  color: #999999;
}
.edit-item-horizontal .content-text.data-v-756671fa::placeholder {
  color: #999999;
}
.edit-item-horizontal .phone-wrapper.data-v-756671fa {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  flex: 1;
  margin-left: 20rpx;
  gap: 12rpx;
}
.edit-item-horizontal .phone-btn.data-v-756671fa {
  background: transparent;
  padding: 0;
  margin: 0;
  font-size: 32rpx;
  color: #333333;
  line-height: 42rpx;
  border: none;
  text-align: right;
}
.edit-item-horizontal .phone-btn.data-v-756671fa::after {
  border: none;
}
.edit-item-horizontal .phone-btn .phone-text.data-v-756671fa {
  color: #000000;
}
.edit-item-horizontal .phone-text.data-v-756671fa {
  color: #000000;
  font-size: 32rpx;
  text-align: right;
}
.save-section.data-v-756671fa {
  padding-top: 20rpx;
}
.save-btn.data-v-756671fa {
  width: 100%;
  height: 88rpx;
  background: linear-gradient(90deg, #4BA1FC 0%, #8f8fff 100%);
  color: #fff;
  border-radius: 44rpx;
  font-size: 32rpx;
  border: none;
}