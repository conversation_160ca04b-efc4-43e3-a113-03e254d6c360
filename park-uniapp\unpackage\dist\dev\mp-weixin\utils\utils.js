"use strict";
const common_vendor = require("../common/vendor.js");
const config_index = require("../config/index.js");
function setStorageSync(key, data) {
  common_vendor.index.setStorageSync(key, data);
}
function getStorageSync(key) {
  return common_vendor.index.getStorageSync(key);
}
const isSameDate = (date1, date2) => {
  if (!date1 || !date2)
    return false;
  return date1.getFullYear() === date2.getFullYear() && date1.getMonth() === date2.getMonth() && date1.getDate() === date2.getDate();
};
const extractDatePart = (dateTimeString) => {
  if (!dateTimeString)
    return "";
  try {
    if (dateTimeString.includes(" ")) {
      return dateTimeString.split(" ")[0];
    } else if (dateTimeString.includes("T")) {
      return dateTimeString.split("T")[0];
    } else {
      const dateRegex = /^\d{4}-\d{2}-\d{2}$/;
      if (dateRegex.test(dateTimeString)) {
        return dateTimeString;
      } else {
        console.warn("日期格式不正确:", dateTimeString);
        return "";
      }
    }
  } catch (error) {
    console.error("提取日期部分失败:", error, dateTimeString);
    return "";
  }
};
const formatDate = (date) => {
  if (!date)
    return "";
  try {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, "0");
    const day = String(date.getDate()).padStart(2, "0");
    return `${year}-${month}-${day}`;
  } catch (error) {
    console.error("格式化日期失败:", error, date);
    return "";
  }
};
const createDate = (dateString) => {
  if (!dateString)
    return /* @__PURE__ */ new Date();
  try {
    if (/^\d{4}-\d{2}-\d{2}$/.test(dateString)) {
      const [year, month, day] = dateString.split("-");
      return new Date(parseInt(year), parseInt(month) - 1, parseInt(day));
    } else {
      return new Date(dateString);
    }
  } catch (error) {
    console.error("创建日期对象失败:", error, dateString);
    return /* @__PURE__ */ new Date();
  }
};
const uploadAvatar = (filePath) => {
  return new Promise((resolve, reject) => {
    common_vendor.index.uploadFile({
      url: config_index.URL + "/file/upload/path",
      filePath,
      name: "file",
      formData: {
        path: "avatar"
      },
      header: {
        "Authorization": "WxBearer " + (common_vendor.index.getStorageSync("token") || "")
      },
      success: (uploadRes) => {
        try {
          const result = JSON.parse(uploadRes.data);
          if (result.code === 200) {
            resolve(result.data.url);
          } else {
            reject(new Error(result.msg || "上传失败"));
          }
        } catch (error) {
          reject(new Error("解析响应失败"));
        }
      },
      fail: (error) => {
        reject(error);
      }
    });
  });
};
exports.createDate = createDate;
exports.extractDatePart = extractDatePart;
exports.formatDate = formatDate;
exports.getStorageSync = getStorageSync;
exports.isSameDate = isSameDate;
exports.setStorageSync = setStorageSync;
exports.uploadAvatar = uploadAvatar;
