<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="872104ba-baaa-4eef-9ff5-85f9386f05a1" name="更改" comment="">
      <change beforePath="$PROJECT_DIR$/lgjy-modules/lgjy-gate/src/main/java/com/lgjy/gate/service/impl/SiZhuoControlServiceImpl.java" beforeDir="false" afterPath="$PROJECT_DIR$/lgjy-modules/lgjy-gate/src/main/java/com/lgjy/gate/service/impl/SiZhuoControlServiceImpl.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/lgjy-modules/lgjy-wx/src/main/java/com/lgjy/wx/controller/WxPackageController.java" beforeDir="false" afterPath="$PROJECT_DIR$/lgjy-modules/lgjy-wx/src/main/java/com/lgjy/wx/controller/WxPackageController.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/lgjy-modules/lgjy-wx/src/main/java/com/lgjy/wx/controller/WxWarehouseController.java" beforeDir="false" afterPath="$PROJECT_DIR$/lgjy-modules/lgjy-wx/src/main/java/com/lgjy/wx/controller/WxWarehouseController.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/lgjy-modules/lgjy-wx/src/main/java/com/lgjy/wx/mapper/WxPackageMapper.java" beforeDir="false" afterPath="$PROJECT_DIR$/lgjy-modules/lgjy-wx/src/main/java/com/lgjy/wx/mapper/WxPackageMapper.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/lgjy-modules/lgjy-wx/src/main/java/com/lgjy/wx/mapper/WxWareHouseMapper.java" beforeDir="false" afterPath="$PROJECT_DIR$/lgjy-modules/lgjy-wx/src/main/java/com/lgjy/wx/mapper/WxWareHouseMapper.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/lgjy-modules/lgjy-wx/src/main/java/com/lgjy/wx/service/WxPackageService.java" beforeDir="false" afterPath="$PROJECT_DIR$/lgjy-modules/lgjy-wx/src/main/java/com/lgjy/wx/service/WxPackageService.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/lgjy-modules/lgjy-wx/src/main/java/com/lgjy/wx/service/WxWareHouseService.java" beforeDir="false" afterPath="$PROJECT_DIR$/lgjy-modules/lgjy-wx/src/main/java/com/lgjy/wx/service/WxWareHouseService.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/lgjy-modules/lgjy-wx/src/main/java/com/lgjy/wx/service/impl/WxPackageServiceImpl.java" beforeDir="false" afterPath="$PROJECT_DIR$/lgjy-modules/lgjy-wx/src/main/java/com/lgjy/wx/service/impl/WxPackageServiceImpl.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/lgjy-modules/lgjy-wx/src/main/java/com/lgjy/wx/service/impl/WxWareHouseServiceImpl.java" beforeDir="false" afterPath="$PROJECT_DIR$/lgjy-modules/lgjy-wx/src/main/java/com/lgjy/wx/service/impl/WxWareHouseServiceImpl.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/lgjy-modules/lgjy-wx/src/main/resources/mapper/wx/WxPackageMapper.xml" beforeDir="false" afterPath="$PROJECT_DIR$/lgjy-modules/lgjy-wx/src/main/resources/mapper/wx/WxPackageMapper.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/lgjy-modules/lgjy-wx/src/main/resources/mapper/wx/WxSpecialUserMapper.xml" beforeDir="false" afterPath="$PROJECT_DIR$/lgjy-modules/lgjy-wx/src/main/resources/mapper/wx/WxSpecialUserMapper.xml" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="Interface" />
        <option value="Enum" />
        <option value="Class" />
      </list>
    </option>
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
    <option name="RESET_MODE" value="KEEP" />
  </component>
  <component name="KubernetesApiPersistence">{}</component>
  <component name="KubernetesApiProvider">{
  &quot;isMigrated&quot;: true
}</component>
  <component name="MavenRunner">
    <option name="skipTests" value="true" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 1
}</component>
  <component name="ProjectId" id="2y1cpHEkv8ul9E8sVYlGOxvHyPG" />
  <component name="ProjectLevelVcsManager">
    <ConfirmationsSetting value="1" id="Add" />
  </component>
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;Docker.lgjy-auth/Dockerfile.executor&quot;: &quot;Run&quot;,
    &quot;Docker.lgjy-file/Dockerfile.executor&quot;: &quot;Run&quot;,
    &quot;Docker.lgjy-gate/Dockerfile.executor&quot;: &quot;Run&quot;,
    &quot;Docker.lgjy-gateway/Dockerfile.executor&quot;: &quot;Run&quot;,
    &quot;Docker.lgjy-system/Dockerfile.executor&quot;: &quot;Run&quot;,
    &quot;Docker.lgjy-wx-auth/Dockerfile.executor&quot;: &quot;Run&quot;,
    &quot;Docker.lgjy-wx/Dockerfile.executor&quot;: &quot;Run&quot;,
    &quot;Maven.lgjy [clean].executor&quot;: &quot;Run&quot;,
    &quot;Maven.lgjy [package].executor&quot;: &quot;Run&quot;,
    &quot;Maven.lgjy [validate].executor&quot;: &quot;Run&quot;,
    &quot;Maven.lgjy-api [clean].executor&quot;: &quot;Run&quot;,
    &quot;Maven.lgjy-api [install].executor&quot;: &quot;Run&quot;,
    &quot;Maven.lgjy-modules [clean].executor&quot;: &quot;Run&quot;,
    &quot;Maven.lgjy-modules [package].executor&quot;: &quot;Run&quot;,
    &quot;Maven.lgjy-sizhuo [clean].executor&quot;: &quot;Run&quot;,
    &quot;Maven.lgjy-sizhuo [install].executor&quot;: &quot;Run&quot;,
    &quot;RequestMappingsPanelOrder0&quot;: &quot;0&quot;,
    &quot;RequestMappingsPanelOrder1&quot;: &quot;1&quot;,
    &quot;RequestMappingsPanelWidth0&quot;: &quot;75&quot;,
    &quot;RequestMappingsPanelWidth1&quot;: &quot;75&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;Spring Boot.LgjyAuthApplication.executor&quot;: &quot;Debug&quot;,
    &quot;Spring Boot.LgjyFileApplication.executor&quot;: &quot;Debug&quot;,
    &quot;Spring Boot.LgjyGateApplication.executor&quot;: &quot;Debug&quot;,
    &quot;Spring Boot.LgjyGatewayApplication.executor&quot;: &quot;Debug&quot;,
    &quot;Spring Boot.LgjyGenApplication.executor&quot;: &quot;Debug&quot;,
    &quot;Spring Boot.LgjyPaymentApplication.executor&quot;: &quot;Debug&quot;,
    &quot;Spring Boot.LgjySystemApplication.executor&quot;: &quot;Debug&quot;,
    &quot;Spring Boot.LgjyWxApplication.executor&quot;: &quot;Debug&quot;,
    &quot;Spring Boot.LgjyWxAuthApplication.executor&quot;: &quot;Debug&quot;,
    &quot;Spring Boot.PaymentApplication.executor&quot;: &quot;Debug&quot;,
    &quot;Spring Boot.RuoYiAuthApplication.executor&quot;: &quot;Debug&quot;,
    &quot;Spring Boot.RuoYiFileApplication.executor&quot;: &quot;Debug&quot;,
    &quot;Spring Boot.RuoYiGatewayApplication.executor&quot;: &quot;Debug&quot;,
    &quot;Spring Boot.RuoYiGenApplication.executor&quot;: &quot;Debug&quot;,
    &quot;Spring Boot.RuoYiJobApplication.executor&quot;: &quot;Debug&quot;,
    &quot;Spring Boot.RuoYiMonitorApplication.executor&quot;: &quot;Debug&quot;,
    &quot;Spring Boot.RuoYiSystemApplication.executor&quot;: &quot;Debug&quot;,
    &quot;Spring Boot.SiZhuoApplication.executor&quot;: &quot;Debug&quot;,
    &quot;git-widget-placeholder&quot;: &quot;park&quot;,
    &quot;jdk.selected.JAVA_MODULE&quot;: &quot;corretto-1.8&quot;,
    &quot;kotlin-language-version-configured&quot;: &quot;true&quot;,
    &quot;last_opened_file_path&quot;: &quot;D:/AAProjectParkingNew/park-api&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,
    &quot;project.structure.last.edited&quot;: &quot;模块&quot;,
    &quot;project.structure.proportion&quot;: &quot;0.15&quot;,
    &quot;project.structure.side.proportion&quot;: &quot;0.2&quot;,
    &quot;run.configurations.included.in.services&quot;: &quot;true&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;reference.settingsDialog.application.dockerMachine&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;
  },
  &quot;keyToStringList&quot;: {
    &quot;DatabaseDriversLRU&quot;: [
      &quot;mysql_aurora_aws&quot;,
      &quot;mysql&quot;
    ]
  }
}</component>
  <component name="ReactorSettings">
    <option name="notificationShown" value="true" />
  </component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="D:\AAProjectParkingNew\park-api" />
      <recent name="D:\AAProjectParkingNew\park-api\lgjy-modules\lgjy-wx\src\main\resources\mapper\wx" />
      <recent name="D:\AAProjectParkingNew\park-api\sql" />
      <recent name="D:\AAProjectParkingNew\park-api\lgjy-api" />
      <recent name="D:\AAProjectParkingNew\park-api\lgjy-modules\lgjy-gate\src\main\resources" />
    </key>
    <key name="CopyClassDialog.RECENTS_KEY">
      <recent name="com.lgjy.wx.domain" />
      <recent name="com.lgjy.wx.mapper" />
      <recent name="com.lgjy.wxauth.rt" />
      <recent name="com.lgjy.gate.config" />
    </key>
  </component>
  <component name="RunAnythingCache">
    <myKeys>
      <visibility group="Gradle 任务" flag="true" />
      <visibility group="Grunt" flag="true" />
      <visibility group="Gulp" flag="true" />
      <visibility group="HTTP Requests" flag="true" />
      <visibility group="HTTP 请求" flag="true" />
      <visibility group="Maven 目标" flag="true" />
      <visibility group="Node.js" flag="true" />
      <visibility group="npm" flag="true" />
      <visibility group="yarn" flag="true" />
      <visibility group="最近的项目" flag="true" />
      <visibility group="运行配置" flag="true" />
    </myKeys>
  </component>
  <component name="RunDashboard">
    <option name="configurationTypes">
      <set>
        <option value="MicronautRunConfigurationType" />
        <option value="QuarkusRunConfigurationType" />
        <option value="SpringBootApplicationConfigurationType" />
      </set>
    </option>
  </component>
  <component name="RunManager" selected="Docker.lgjy-gate/Dockerfile">
    <configuration default="true" type="JetRunConfigurationType">
      <module name="lgjy-auth" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration default="true" type="KotlinStandaloneScriptRunConfigurationType">
      <module name="lgjy-auth" />
      <option name="filePath" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="LgjyAuthApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="lgjy-auth" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.lgjy.auth.LgjyAuthApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="LgjyFileApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="lgjy-modules-file" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.lgjy.file.LgjyFileApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="LgjyGateApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="lgjy-modules-gate" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.lgjy.gate.LgjyGateApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="LgjyGatewayApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="lgjy-gateway" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.lgjy.gateway.LgjyGatewayApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="LgjySystemApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" temporary="true" nameIsGenerated="true">
      <module name="lgjy-modules-system" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.lgjy.system.LgjySystemApplication" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.lgjy.system.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="LgjyWxApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="lgjy-modules-wx" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.lgjy.wx.LgjyWxApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="LgjyWxAuthApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="lgjy-wx-auth" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.lgjy.wxauth.LgjyWxAuthApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="lgjy-auth/Dockerfile" type="docker-deploy" factoryName="dockerfile" server-name="Docker">
      <deployment type="dockerfile">
        <settings>
          <option name="imageTag" value="park/lgjy-auth:3.6.9" />
          <option name="buildOnly" value="true" />
          <option name="containerName" value="" />
          <option name="sourceFilePath" value="lgjy-auth/Dockerfile" />
        </settings>
      </deployment>
      <method v="2" />
    </configuration>
    <configuration name="lgjy-file/Dockerfile" type="docker-deploy" factoryName="dockerfile" temporary="true" server-name="Docker">
      <deployment type="dockerfile">
        <settings>
          <option name="imageTag" value="park/lgjy-file:3.6.9" />
          <option name="buildOnly" value="true" />
          <option name="sourceFilePath" value="lgjy-modules/lgjy-file/Dockerfile" />
        </settings>
      </deployment>
      <method v="2" />
    </configuration>
    <configuration name="lgjy-gate/Dockerfile" type="docker-deploy" factoryName="dockerfile" server-name="Docker">
      <deployment type="dockerfile">
        <settings>
          <option name="imageTag" value="park/lgjy-gate:3.6.9" />
          <option name="buildOnly" value="true" />
          <option name="containerName" value="" />
          <option name="sourceFilePath" value="lgjy-modules/lgjy-gate/Dockerfile" />
        </settings>
      </deployment>
      <method v="2" />
    </configuration>
    <configuration name="lgjy-gateway/Dockerfile" type="docker-deploy" factoryName="dockerfile" server-name="Docker">
      <deployment type="dockerfile">
        <settings>
          <option name="imageTag" value="park/lgjy-gateway:3.6.9" />
          <option name="buildOnly" value="true" />
          <option name="containerName" value="" />
          <option name="sourceFilePath" value="lgjy-gateway/Dockerfile" />
        </settings>
      </deployment>
      <method v="2" />
    </configuration>
    <configuration name="lgjy-system/Dockerfile" type="docker-deploy" factoryName="dockerfile" server-name="Docker">
      <deployment type="dockerfile">
        <settings>
          <option name="imageTag" value="park/lgjy-system:3.6.9" />
          <option name="buildOnly" value="true" />
          <option name="containerName" value="" />
          <option name="sourceFilePath" value="lgjy-modules/lgjy-system/Dockerfile" />
        </settings>
      </deployment>
      <method v="2" />
    </configuration>
    <configuration name="lgjy-wx-auth/Dockerfile" type="docker-deploy" factoryName="dockerfile" server-name="Docker">
      <deployment type="dockerfile">
        <settings>
          <option name="imageTag" value="park/lgjy-wx-auth:3.6.9" />
          <option name="buildOnly" value="true" />
          <option name="containerName" value="" />
          <option name="sourceFilePath" value="lgjy-wx-auth/Dockerfile" />
        </settings>
      </deployment>
      <method v="2" />
    </configuration>
    <configuration name="lgjy-wx/Dockerfile" type="docker-deploy" factoryName="dockerfile" server-name="Docker">
      <deployment type="dockerfile">
        <settings>
          <option name="imageTag" value="park/lgjy-wx:3.6.9" />
          <option name="buildOnly" value="true" />
          <option name="sourceFilePath" value="lgjy-modules/lgjy-wx/Dockerfile" />
        </settings>
      </deployment>
      <method v="2" />
    </configuration>
    <list>
      <item itemvalue="Docker.lgjy-gate/Dockerfile" />
      <item itemvalue="Docker.lgjy-wx-auth/Dockerfile" />
      <item itemvalue="Docker.lgjy-wx/Dockerfile" />
      <item itemvalue="Docker.lgjy-auth/Dockerfile" />
      <item itemvalue="Docker.lgjy-system/Dockerfile" />
      <item itemvalue="Docker.lgjy-gateway/Dockerfile" />
      <item itemvalue="Docker.lgjy-file/Dockerfile" />
      <item itemvalue="Spring Boot.LgjyWxApplication" />
      <item itemvalue="Spring Boot.LgjyGateApplication" />
      <item itemvalue="Spring Boot.LgjyFileApplication" />
      <item itemvalue="Spring Boot.LgjyWxAuthApplication" />
      <item itemvalue="Spring Boot.LgjyAuthApplication" />
      <item itemvalue="Spring Boot.LgjyGatewayApplication" />
      <item itemvalue="Spring Boot.LgjySystemApplication" />
    </list>
    <recent_temporary>
      <list>
        <item itemvalue="Spring Boot.LgjySystemApplication" />
        <item itemvalue="Docker.lgjy-file/Dockerfile" />
      </list>
    </recent_temporary>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-jdk-9f38398b9061-39b83d9b5494-intellij.indexing.shared.core-IU-241.18034.62" />
        <option value="bundled-js-predefined-1d06a55b98c1-0b3e54e931b4-JavaScript-IU-241.18034.62" />
      </set>
    </attachedChunks>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="应用程序级" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="默认任务">
      <changelist id="872104ba-baaa-4eef-9ff5-85f9386f05a1" name="更改" comment="" />
      <created>1721397813121</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1721397813121</updated>
      <workItem from="1749005702074" duration="9060000" />
      <workItem from="1749021397249" duration="2736000" />
      <workItem from="1749047425665" duration="5623000" />
      <workItem from="1749081468763" duration="1000" />
      <workItem from="1749081980638" duration="13000" />
      <workItem from="1749082117028" duration="966000" />
      <workItem from="1749171510829" duration="6266000" />
      <workItem from="1749184740891" duration="4589000" />
      <workItem from="1749256694441" duration="15879000" />
      <workItem from="1749341844217" duration="16816000" />
      <workItem from="1749431307666" duration="6714000" />
      <workItem from="1749459763095" duration="3136000" />
      <workItem from="1749517027073" duration="608000" />
      <workItem from="1749521217067" duration="15831000" />
      <workItem from="1749594112650" duration="5577000" />
      <workItem from="1749604129677" duration="16503000" />
      <workItem from="1749690511219" duration="9601000" />
      <workItem from="1749719933215" duration="6605000" />
      <workItem from="1749776498914" duration="12019000" />
      <workItem from="1749862120965" duration="35308000" />
      <workItem from="1750080119353" duration="725000" />
      <workItem from="1750122357283" duration="14050000" />
      <workItem from="1750209050278" duration="9912000" />
      <workItem from="1750230739854" duration="2367000" />
      <workItem from="1750250575956" duration="2909000" />
      <workItem from="1750296758753" duration="22450000" />
      <workItem from="1750384353891" duration="16187000" />
      <workItem from="1750417174345" duration="15184000" />
      <workItem from="1750575355647" duration="16230000" />
      <workItem from="1750727716280" duration="10542000" />
      <workItem from="1750772218699" duration="6355000" />
      <workItem from="1750814537635" duration="18497000" />
      <workItem from="1750900001047" duration="19878000" />
      <workItem from="1750940522847" duration="10639000" />
      <workItem from="1750986847186" duration="11253000" />
      <workItem from="1751163968362" duration="1499000" />
      <workItem from="1751186263121" duration="710000" />
      <workItem from="1751258187856" duration="1336000" />
      <workItem from="1751286679973" duration="1340000" />
      <workItem from="1751332243106" duration="13797000" />
      <workItem from="1751418449734" duration="15097000" />
      <workItem from="1751457456391" duration="4991000" />
      <workItem from="1751504828471" duration="11833000" />
      <workItem from="1751591407586" duration="18630000" />
      <workItem from="1751682832174" duration="11535000" />
      <workItem from="1751761991080" duration="11367000" />
      <workItem from="1751850530732" duration="2074000" />
      <workItem from="1751937546520" duration="14320000" />
      <workItem from="1751968906940" duration="5763000" />
      <workItem from="1752023265171" duration="9920000" />
      <workItem from="1752109576204" duration="4328000" />
      <workItem from="1752116455395" duration="15037000" />
      <workItem from="1752137802292" duration="16416000" />
      <workItem from="1752195932629" duration="23400000" />
      <workItem from="1752277032199" duration="21037000" />
      <workItem from="1752455143470" duration="23000" />
      <workItem from="1752456456034" duration="14785000" />
      <workItem from="1752480969389" duration="4258000" />
      <workItem from="1752541830032" duration="13752000" />
      <workItem from="1752578705134" duration="5871000" />
      <workItem from="1752627890238" duration="10467000" />
      <workItem from="1752655443245" duration="4710000" />
      <workItem from="1752714467620" duration="11671000" />
      <workItem from="1752746833789" duration="647000" />
      <workItem from="1752800995654" duration="12229000" />
      <workItem from="1752826269652" duration="5093000" />
      <workItem from="1752909345105" duration="775000" />
      <workItem from="1752920400501" duration="993000" />
      <workItem from="1752976754259" duration="14862000" />
      <workItem from="1753060032777" duration="16961000" />
      <workItem from="1753103059091" duration="7661000" />
      <workItem from="1753146563189" duration="16747000" />
      <workItem from="1753174864293" duration="6617000" />
      <workItem from="1753232981493" duration="20525000" />
      <workItem from="1753271899510" duration="13750000" />
      <workItem from="1753320161044" duration="19578000" />
      <workItem from="1753358351163" duration="14177000" />
      <workItem from="1753406161730" duration="15249000" />
      <workItem from="1753428765361" duration="112000" />
      <workItem from="1753428890696" duration="248000" />
      <workItem from="1753435060533" duration="8414000" />
      <workItem from="1753496139358" duration="28974000" />
      <workItem from="1753542085473" duration="439000" />
      <workItem from="1753543448894" duration="2126000" />
      <workItem from="1753545612401" duration="1675000" />
      <workItem from="1753589675750" duration="7752000" />
      <workItem from="1753605699265" duration="10656000" />
      <workItem from="1753665771185" duration="11995000" />
      <workItem from="1753683269034" duration="4354000" />
      <workItem from="1753691743482" duration="14479000" />
      <workItem from="1753751454796" duration="19991000" />
      <workItem from="1753794849190" duration="10766000" />
      <workItem from="1753838468706" duration="6149000" />
      <workItem from="1753856593372" duration="11852000" />
      <workItem from="1753889398578" duration="121000" />
      <workItem from="1753924367145" duration="16061000" />
      <workItem from="1754011934975" duration="1985000" />
      <workItem from="1754014099099" duration="8496000" />
      <workItem from="1754109371820" duration="1762000" />
      <workItem from="1754189033609" duration="15033000" />
      <workItem from="1754270026165" duration="13579000" />
      <workItem from="1754356987910" duration="14932000" />
      <workItem from="1754382667195" duration="1405000" />
      <workItem from="1754442702194" duration="675000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="TAB_STATES">
      <map>
        <entry key="MAIN">
          <value>
            <State />
          </value>
        </entry>
      </map>
    </option>
  </component>
  <component name="XDebuggerManager">
    <watches-manager>
      <configuration name="SpringBootApplicationConfigurationType">
        <watch expression="if (StringUtils.isNotEmpty(token)) {&#10;//            // 裁剪前缀（Web端或小程序端）&#10;//            if (token.startsWith(TokenConstants.PREFIX)) {&#10;//                token = token.replaceFirst(TokenConstants.PREFIX, StringUtils.EMPTY);&#10;//            } else if (token.startsWith(TokenConstants.WX_PREFIX)) {&#10;//                token = token.replaceFirst(TokenConstants.WX_PREFIX, StringUtils.EMPTY);&#10;//            }&#10;//        }" />
      </configuration>
    </watches-manager>
  </component>
  <component name="XSLT-Support.FileAssociations.UIState">
    <expand />
    <select />
  </component>
</project>