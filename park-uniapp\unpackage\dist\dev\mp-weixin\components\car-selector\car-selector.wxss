/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* uni.scss */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.car-selector-dropdown.data-v-ee5b4748 {
  background: #fff;
  border-radius: 20rpx 20rpx 0 0;
  max-height: 60vh;
  display: flex;
  flex-direction: column;
}
.car-selector-dropdown .dropdown-header.data-v-ee5b4748 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx 40rpx;
  border-bottom: 1rpx solid #f0f0f0;
}
.car-selector-dropdown .dropdown-header .dropdown-title.data-v-ee5b4748 {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}
.car-selector-dropdown .dropdown-content.data-v-ee5b4748 {
  flex: 1;
  overflow-y: auto;
  padding: 0 40rpx;
  max-height: 400rpx;
}
.car-selector-dropdown .dropdown-content .dropdown-item.data-v-ee5b4748 {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 25rpx 0;
  border-bottom: 1rpx solid #f8f8f8;
}
.car-selector-dropdown .dropdown-content .dropdown-item.data-v-ee5b4748:last-child {
  border-bottom: none;
}
.car-selector-dropdown .dropdown-content .dropdown-item .car-plate.data-v-ee5b4748 {
  font-size: 28rpx;
  color: #333;
  flex: 1;
}
.car-selector-dropdown .dropdown-footer.data-v-ee5b4748 {
  padding: 30rpx 40rpx;
  border-top: 1rpx solid #f0f0f0;
}
.car-selector-dropdown .dropdown-footer .confirm-btn.data-v-ee5b4748 {
  width: 100%;
  height: 80rpx;
  background: linear-gradient(90deg, #4BA1FC 0%, #7e6dff 100%);
  color: #fff;
  border: none;
  border-radius: 40rpx;
  font-size: 28rpx;
  font-weight: bold;
}