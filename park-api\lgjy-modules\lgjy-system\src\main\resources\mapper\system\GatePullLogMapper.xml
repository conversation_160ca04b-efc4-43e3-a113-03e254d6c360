<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lgjy.system.mapper.GatePullLogMapper">
    
    <resultMap type="GatePullLog" id="GatePullLogResult">
        <result property="id"    column="id"    />
        <result property="parkingName"    column="parking_name"    />
        <result property="accessAddress"    column="access_address"    />
        <result property="address"    column="address"    />
        <result property="data"    column="data"    />
        <result property="lastUpdate"    column="last_update"    />
    </resultMap>

    <sql id="selectGatePullLogVo">
        select id, parking_name, access_address, address, data, last_update from gate_pull_log
    </sql>

    <select id="selectGatePullLogList" parameterType="GatePullLog" resultMap="GatePullLogResult">
        <include refid="selectGatePullLogVo"/>
        <where>
            <if test="parkingName != null  and parkingName != ''"> and parking_name like concat('%', #{parkingName}, '%')</if>
            <if test="accessAddress != null  and accessAddress != ''"> and access_address like concat('%', #{accessAddress}, '%')</if>
            <if test="address != null  and address != ''"> and address like concat('%', #{address}, '%')</if>
            <if test="data != null  and data != ''"> and data like concat('%', #{data}, '%')</if>
            <if test="lastUpdate != null  and lastUpdate != ''"> and DATE(last_update) = #{lastUpdate}</if>
        </where>
        order by last_update desc
    </select>
    
    <select id="selectGatePullLogById" parameterType="String" resultMap="GatePullLogResult">
        <include refid="selectGatePullLogVo"/>
        where id = #{id}
    </select>

    <delete id="deleteGatePullLogById" parameterType="String">
        delete from gate_pull_log where id = #{id}
    </delete>

    <delete id="deleteGatePullLogByIds" parameterType="String">
        delete from gate_pull_log where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="cleanGatePullLog">
        truncate table gate_pull_log
    </delete>

</mapper>
