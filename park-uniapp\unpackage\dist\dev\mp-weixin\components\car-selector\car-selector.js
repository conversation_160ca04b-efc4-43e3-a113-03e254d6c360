"use strict";
const common_vendor = require("../../common/vendor.js");
if (!Array) {
  const _easycom_u_icon2 = common_vendor.resolveComponent("u-icon");
  const _easycom_u_popup2 = common_vendor.resolveComponent("u-popup");
  (_easycom_u_icon2 + _easycom_u_popup2)();
}
const _easycom_u_icon = () => "../../node-modules/uview-plus/components/u-icon/u-icon.js";
const _easycom_u_popup = () => "../../node-modules/uview-plus/components/u-popup/u-popup.js";
if (!Math) {
  (_easycom_u_icon + _easycom_u_popup)();
}
const _sfc_main = {
  __name: "car-selector",
  props: {
    show: {
      type: <PERSON>olean,
      default: false
    },
    carList: {
      type: Array,
      default: () => []
    },
    maxSelect: {
      type: Number,
      default: 1
    },
    modelValue: {
      type: Array,
      default: () => []
    }
  },
  emits: ["update:modelValue", "close", "confirm"],
  setup(__props, { emit: __emit }) {
    const props = __props;
    const emit = __emit;
    const selectedCars = common_vendor.ref([]);
    common_vendor.watch(() => props.modelValue, (newVal) => {
      selectedCars.value = [...newVal];
    }, { immediate: true });
    const toggleCarSelection = (car) => {
      const index = selectedCars.value.findIndex((item) => item.plateNo === car.plateNo);
      if (index > -1) {
        selectedCars.value.splice(index, 1);
      } else {
        if (selectedCars.value.length >= props.maxSelect) {
          if (props.maxSelect === 1) {
            selectedCars.value = [car];
          } else {
            common_vendor.index.showToast({
              title: `最多只能选择${props.maxSelect}辆车`,
              icon: "none",
              duration: 2e3
            });
            return;
          }
        } else {
          selectedCars.value.push(car);
        }
      }
    };
    const isCarSelected = (car) => {
      return selectedCars.value.some((item) => item.plateNo === car.plateNo);
    };
    const confirmSelection = () => {
      if (selectedCars.value.length === 0) {
        common_vendor.index.showToast({
          title: "请至少选择一辆车",
          icon: "none",
          duration: 2e3
        });
        return;
      }
      emit("update:modelValue", selectedCars.value);
      emit("confirm", selectedCars.value);
      emit("close");
    };
    const handleClose = () => {
      emit("close");
    };
    return (_ctx, _cache) => {
      return {
        a: common_vendor.o(handleClose),
        b: common_vendor.p({
          name: "close",
          size: "20",
          color: "#666"
        }),
        c: common_vendor.f(__props.carList, (car, index, i0) => {
          return {
            a: common_vendor.t(car.plateNo),
            b: "ee5b4748-2-" + i0 + ",ee5b4748-0",
            c: common_vendor.p({
              name: isCarSelected(car) ? "checkmark-circle-fill" : "checkmark-circle",
              color: isCarSelected(car) ? "#4BA1FC" : "#c8c9cc",
              size: "20"
            }),
            d: index,
            e: common_vendor.o(($event) => toggleCarSelection(car), index)
          };
        }),
        d: common_vendor.t(selectedCars.value.length),
        e: common_vendor.t(__props.maxSelect),
        f: common_vendor.o(confirmSelection),
        g: common_vendor.o(handleClose),
        h: common_vendor.p({
          show: __props.show,
          mode: "bottom"
        })
      };
    };
  }
};
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-ee5b4748"]]);
wx.createComponent(Component);
