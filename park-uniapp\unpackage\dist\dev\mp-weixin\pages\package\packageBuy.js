"use strict";
const common_vendor = require("../../common/vendor.js");
const common_assets = require("../../common/assets.js");
const api_package = require("../../api/package.js");
const api_login = require("../../api/login.js");
const utils_utils = require("../../utils/utils.js");
const api_warehouse = require("../../api/warehouse.js");
if (!Array) {
  const _easycom_u_picker2 = common_vendor.resolveComponent("u-picker");
  _easycom_u_picker2();
}
const _easycom_u_picker = () => "../../node-modules/uview-plus/components/u-picker/u-picker.js";
if (!Math) {
  (WarehouseSelector + _easycom_u_picker + CarSelector)();
}
const WarehouseSelector = () => "../../components/warehouse-selector/warehouse-selector.js";
const CarSelector = () => "../../components/car-selector/car-selector.js";
const _sfc_main = {
  __name: "packageBuy",
  setup(__props) {
    const packageOrder = common_vendor.ref({});
    common_vendor.ref(false);
    const isCarInWarehouse = common_vendor.ref(false);
    common_vendor.ref({});
    const canSelectTime = common_vendor.ref(false);
    const selectedDate = common_vendor.ref("");
    const minDate = common_vendor.ref("");
    const maxDate = common_vendor.ref("");
    const wareHouseList = common_vendor.ref([]);
    const wareHouseSelector = common_vendor.ref(false);
    const currentWarehouse = common_vendor.ref({ id: 0, name: "请选择场库" });
    const operationTypeList = common_vendor.ref([
      { id: 0, name: "购买", isRenewal: false },
      { id: 1, name: "续费", isRenewal: true }
    ]);
    const showOperationTypeSelector = common_vendor.ref(false);
    const currentOperationType = common_vendor.ref({ id: null, name: "请选择操作类型", isRenewal: false });
    const carList = common_vendor.ref([]);
    const selectedCars = common_vendor.ref([]);
    const showCarSelector = common_vendor.ref(false);
    const noticeText = common_vendor.computed(() => {
      if (packageOrder.value.isRenewal) {
        return `当前选择的续费套餐是：${packageOrder.value.packageName || ""}(${packageOrder.value.packageDays || ""}个自然日)。
        续费将在原有套餐到期日期基础上延长，无法修改时间`;
      }
      return `当前选择的开通套餐是：${packageOrder.value.packageName || ""}(${packageOrder.value.packageDays || ""}个自然日)。
    请选择场库、操作类型和车辆，您可以在此页面选择开始日期。`;
    });
    common_vendor.onLoad(async (options) => {
      const packageInfo = JSON.parse(options.packageOrder);
      packageOrder.value = {
        ...packageInfo,
        warehouseId: null,
        warehouseName: "",
        plateNo: "",
        isRenewal: false,
        beginVipTime: "",
        expirationTime: ""
      };
      console.log("packageOrder.value: ", packageOrder.value);
      await initWarehouseData();
    });
    const handleCarNotInWarehouse = () => {
      canSelectTime.value = true;
      const today = /* @__PURE__ */ new Date();
      const dateStr = utils_utils.formatDate(today);
      packageOrder.value.beginVipTime = `${dateStr} 00:00:00`;
      updateDateRange();
      calculateEndDate();
    };
    const updateDateRange = () => {
      if (!canSelectTime.value) {
        return;
      }
      const today = /* @__PURE__ */ new Date();
      minDate.value = utils_utils.formatDate(today);
      const maxDateObj = /* @__PURE__ */ new Date();
      maxDateObj.setMonth(today.getMonth() + 3);
      maxDate.value = utils_utils.formatDate(maxDateObj);
      selectedDate.value = utils_utils.formatDate(today);
    };
    const onDateChange = (e) => {
      if (!canSelectTime.value) {
        return;
      }
      const selectedDateStr = e.detail.value;
      const selectedDateObj = utils_utils.createDate(selectedDateStr);
      const today = /* @__PURE__ */ new Date();
      utils_utils.isSameDate(selectedDateObj, today);
      packageOrder.value.beginVipTime = `${selectedDateStr} 00:00:00`;
      selectedDate.value = selectedDateStr;
      calculateEndDate();
    };
    const calculateEndDate = () => {
      if (packageOrder.value.beginVipTime && packageOrder.value.packageDays) {
        const startDate = new Date(packageOrder.value.beginVipTime);
        const today = /* @__PURE__ */ new Date();
        const endDate = new Date(startDate);
        const isToday = utils_utils.isSameDate(startDate, today);
        if (isToday) {
          endDate.setDate(startDate.getDate() + packageOrder.value.packageDays);
        } else {
          endDate.setDate(startDate.getDate() + packageOrder.value.packageDays - 1);
        }
        const endDateStr = utils_utils.formatDate(endDate);
        packageOrder.value.expirationTime = `${endDateStr} 23:59:59`;
      }
    };
    const submitOrder = () => {
      if (!packageOrder.value.warehouseId) {
        common_vendor.index.showToast({
          title: "请选择场库",
          icon: "none",
          duration: 2e3
        });
        return;
      }
      if (!packageOrder.value.plateNo) {
        common_vendor.index.showToast({
          title: "请选择车辆",
          icon: "none",
          duration: 2e3
        });
        return;
      }
      console.log("packageOrder.value: ", packageOrder.value);
      proceedToSubmit();
    };
    const createOrderWithOpenid = () => {
      common_vendor.index.showLoading({
        title: "加载中...",
        mask: true
      });
      api_package.createOrder(packageOrder.value).then((res) => {
        console.log("创建订单 res: ", res);
        if (res.data.needPay) {
          common_vendor.index.requestPayment({
            timeStamp: res.data.timeStamp,
            nonceStr: res.data.nonceStr,
            package: res.data.package,
            signType: res.data.signType,
            paySign: res.data.paySign,
            success: function(result) {
              common_vendor.index.hideLoading();
              setTimeout(() => {
                common_vendor.index.showToast({
                  title: "支付成功~",
                  icon: "none",
                  duration: 2e3
                });
              }, 100);
              setTimeout(() => {
                common_vendor.index.navigateBack();
              }, 2e3);
            },
            fail: function(err) {
              common_vendor.index.hideLoading();
              console.log("支付失败的回调：", err);
              if (res.data.orderId) {
                api_package.updateOrder({
                  id: res.data.orderId,
                  payStatus: 3
                  // 已取消
                }).then((updateRes) => {
                  console.log("订单状态更新为已取消：", updateRes);
                }).catch((updateErr) => {
                  console.log("订单状态更新失败：", updateErr);
                });
              }
              setTimeout(() => {
                common_vendor.index.showToast({
                  title: "支付失败",
                  icon: "none",
                  duration: 1500
                });
              }, 100);
              setTimeout(() => {
                common_vendor.index.navigateBack();
              }, 2e3);
            },
            complete: function(res2) {
              common_vendor.index.hideLoading();
            }
          });
        } else {
          common_vendor.index.hideLoading();
          setTimeout(() => {
            common_vendor.index.showToast({
              title: "开通成功~",
              icon: "none",
              duration: 2e3
            });
          }, 100);
          setTimeout(() => {
            common_vendor.index.navigateBack();
          }, 2e3);
        }
      }).catch((err) => {
        console.log(err);
        common_vendor.index.hideLoading();
        setTimeout(() => {
          common_vendor.index.showToast({
            title: "订单创建失败",
            icon: "none",
            duration: 1500
          });
        }, 100);
        setTimeout(() => {
          common_vendor.index.navigateBack();
        }, 2e3);
      });
    };
    const initWarehouseData = async () => {
      try {
        const res = await api_warehouse.getParkWareHouseList();
        wareHouseList.value = res.data.map((item) => ({
          id: item.id,
          name: item.warehouseName,
          latitude: item.latitude,
          longitude: item.longitude,
          parkingSpaceType: item.parkingSpaceType
        }));
      } catch (error) {
        console.error("获取场库列表失败:", error);
      }
    };
    const selectCarsByType = async () => {
      if (!packageOrder.value.warehouseId) {
        common_vendor.index.showToast({
          title: "请先选择场库",
          icon: "none",
          duration: 2e3
        });
        return;
      }
      if (currentOperationType.value.id === null) {
        common_vendor.index.showToast({
          title: "请先选择操作类型",
          icon: "none",
          duration: 2e3
        });
        return;
      }
      try {
        common_vendor.index.showLoading({ title: "查询中..." });
        if (currentOperationType.value.id === 1) {
          const res = await api_package.getRenewalCarList({
            warehouseId: packageOrder.value.warehouseId,
            vipType: packageOrder.value.vipType
          });
          const renewalCars = res.data || [];
          carList.value = renewalCars.map((item) => ({
            plateNo: item.plateNo || "",
            ...item
          }));
          if (carList.value.length === 0) {
            common_vendor.index.showToast({
              title: "暂无可续费的车牌",
              icon: "none",
              duration: 2e3
            });
            return;
          }
        } else {
          const res = await api_package.getPurchaseCarList({
            warehouseId: packageOrder.value.warehouseId,
            vipType: packageOrder.value.vipType
          });
          const purchaseCars = res.data || [];
          carList.value = purchaseCars.map((item) => ({
            plateNo: item.plateNo || "",
            ...item
          }));
          if (carList.value.length === 0) {
            common_vendor.index.showToast({
              title: "暂无可购买的车牌",
              icon: "none",
              duration: 2e3
            });
            return;
          }
        }
        showCarSelector.value = true;
      } catch (error) {
        console.error("查询车辆失败:", error);
        common_vendor.index.showToast({
          title: "查询失败，请重试",
          icon: "none",
          duration: 2e3
        });
      } finally {
        common_vendor.index.hideLoading();
      }
    };
    const showWarehouseSelector = () => {
      wareHouseSelector.value = true;
    };
    const closeWarehouseSelector = () => {
      wareHouseSelector.value = false;
    };
    const selectWarehouse = (warehouse) => {
      currentWarehouse.value = warehouse;
      packageOrder.value.warehouseId = warehouse.id;
      packageOrder.value.warehouseName = warehouse.name;
      common_vendor.index.setStorageSync("currentWarehouse", warehouse);
      closeWarehouseSelector();
    };
    const openOperationTypeSelector = () => {
      if (!packageOrder.value.warehouseId) {
        common_vendor.index.showToast({
          title: "请先选择场库",
          icon: "none",
          duration: 2e3
        });
        return;
      }
      showOperationTypeSelector.value = true;
    };
    const onOperationTypeConfirm = (e) => {
      currentOperationType.value = e.value[0];
      packageOrder.value.isRenewal = currentOperationType.value.isRenewal;
      showOperationTypeSelector.value = false;
      selectedCars.value = [];
      packageOrder.value.plateNo = "";
      carList.value = [];
      showCarSelector.value = false;
    };
    const getSelectedCarsDisplay = () => {
      if (selectedCars.value.length === 0) {
        return "请选择车牌";
      } else {
        return selectedCars.value.map((car) => car.plateNo).join(",");
      }
    };
    const onCarSelectionConfirm = (selectedCarList) => {
      packageOrder.value.plateNo = selectedCarList.map((car) => car.plateNo).join(",");
      if (packageOrder.value.isRenewal && selectedCarList.length === 1) {
        const selectedCar = selectedCarList[0];
        packageOrder.value.beginVipTime = selectedCar.beginVipTime;
        packageOrder.value.expirationTime = selectedCar.endVipTime;
        if (selectedCar.endVipTime && packageOrder.value.packageDays) {
          const originalEndDate = new Date(selectedCar.endVipTime);
          const newEndDate = new Date(originalEndDate);
          newEndDate.setDate(originalEndDate.getDate() + packageOrder.value.packageDays);
          const endDateStr = utils_utils.formatDate(newEndDate);
          packageOrder.value.newExpirationTime = `${endDateStr} 23:59:59`;
        }
      }
      try {
        common_vendor.index.setStorageSync("selectedCars", selectedCarList);
      } catch (error) {
        console.error("缓存车辆信息失败:", error);
      }
      if (!packageOrder.value.isRenewal) {
        calculateTimeInfo();
      }
    };
    const calculateTimeInfo = () => {
      isCarInWarehouse.value = false;
      handleCarNotInWarehouse();
    };
    const proceedToSubmit = () => {
      common_vendor.index.login({
        success: async (loginRes) => {
          try {
            const openidRes = await api_login.getOpenid({
              wxCode: loginRes.code
            });
            packageOrder.value.openid = openidRes.data;
            createOrderWithOpenid();
          } catch (error) {
            console.error("获取openid失败:", error);
            common_vendor.index.showToast({
              title: "获取用户信息失败",
              icon: "none",
              duration: 2e3
            });
          }
        },
        fail: (error) => {
          console.error("登录失败:", error);
          common_vendor.index.showToast({
            title: "登录失败，请重试",
            icon: "none",
            duration: 2e3
          });
        }
      });
    };
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_vendor.t(noticeText.value),
        b: common_assets._imports_1$3,
        c: common_vendor.t(currentWarehouse.value.name),
        d: common_vendor.o(showWarehouseSelector),
        e: common_assets._imports_2$1,
        f: common_vendor.t(currentOperationType.value.name),
        g: common_vendor.o(openOperationTypeSelector),
        h: common_assets._imports_2$1,
        i: common_vendor.t(getSelectedCarsDisplay()),
        j: common_vendor.o(selectCarsByType),
        k: common_assets._imports_3,
        l: common_vendor.t(packageOrder.value.packageName || "-"),
        m: common_assets._imports_4,
        n: canSelectTime.value
      }, canSelectTime.value ? {
        o: common_vendor.t(packageOrder.value.beginVipTime || "请选择开始时间"),
        p: selectedDate.value,
        q: minDate.value,
        r: maxDate.value,
        s: common_vendor.o(onDateChange)
      } : {
        t: common_vendor.t(packageOrder.value.beginVipTime || "请选择开始时间")
      }, {
        v: common_vendor.n(canSelectTime.value ? "clickable-text" : ""),
        w: packageOrder.value.isRenewal
      }, packageOrder.value.isRenewal ? {
        x: common_assets._imports_4,
        y: common_vendor.t(packageOrder.value.expirationTime)
      } : {}, {
        z: common_assets._imports_4,
        A: common_vendor.t(packageOrder.value.isRenewal ? "续费后到期时间" : "到期时间"),
        B: common_vendor.t(packageOrder.value.newExpirationTime || packageOrder.value.expirationTime || "--"),
        C: common_assets._imports_5,
        D: common_vendor.t(packageOrder.value.packagePrice || "-"),
        E: common_assets._imports_0$6,
        F: common_vendor.t(packageOrder.value.packagePrice || "0.00"),
        G: common_vendor.t(packageOrder.value.isRenewal ? "确认续费" : "提交订单"),
        H: common_vendor.o(submitOrder),
        I: common_vendor.o(closeWarehouseSelector),
        J: common_vendor.o(selectWarehouse),
        K: common_vendor.p({
          show: wareHouseSelector.value,
          ["warehouse-list"]: wareHouseList.value,
          ["current-warehouse"]: currentWarehouse.value,
          ["window-height-half"]: 400
        }),
        L: common_vendor.o(onOperationTypeConfirm),
        M: common_vendor.o(($event) => showOperationTypeSelector.value = false),
        N: common_vendor.p({
          show: showOperationTypeSelector.value,
          columns: [operationTypeList.value],
          keyName: "name"
        }),
        O: common_vendor.o(($event) => showCarSelector.value = false),
        P: common_vendor.o(onCarSelectionConfirm),
        Q: common_vendor.o(($event) => selectedCars.value = $event),
        R: common_vendor.p({
          show: showCarSelector.value,
          ["car-list"]: carList.value,
          ["max-select"]: currentOperationType.value.id === 1 ? 1 : currentWarehouse.value.parkingSpaceType || 1,
          modelValue: selectedCars.value
        })
      });
    };
  }
};
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-e922d8e0"]]);
wx.createPage(MiniProgramPage);
