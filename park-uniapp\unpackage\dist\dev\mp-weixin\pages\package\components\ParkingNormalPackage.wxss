/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* uni.scss */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.vip-page.data-v-f658ade8 {
  min-height: 80vh;
  position: relative;
}
.vip-bg.data-v-f658ade8 {
  position: absolute;
  top: -15rpx;
  left: 0;
  width: 100%;
  z-index: 1;
}
.vip-card.data-v-f658ade8 {
  position: relative;
  z-index: 10;
  margin: 15rpx 0 0 50rpx;
  height: 320rpx;
}
.vip-card .vip-info.data-v-f658ade8 {
  padding: 26rpx 22rpx;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  color: #fff;
}
.vip-card .vip-info .vip-title-container.data-v-f658ade8 {
  display: flex;
  align-items: center;
  gap: 16rpx;
  cursor: pointer;
}
.vip-card .vip-info .vip-title-container .vip-title.data-v-f658ade8 {
  font-size: 45rpx;
  font-weight: bold;
}
.vip-card .vip-info .vip-plate.data-v-f658ade8 {
  font-size: 30rpx;
  margin: 20rpx 0;
}
.vip-card .vip-info .vip-plate .plate-label.data-v-f658ade8 {
  font-size: 28rpx;
  color: #fff;
  margin-bottom: 16rpx;
  opacity: 0.9;
}
.vip-card .vip-info .vip-plate .plate-info.data-v-f658ade8 {
  display: flex;
  align-items: center;
}
.vip-card .vip-info .vip-plate .plate-info .plate-number.data-v-f658ade8 {
  margin-right: 10rpx;
  font-size: 30rpx;
  font-weight: 500;
  color: #fff;
}
.vip-card .vip-info .vip-expire.data-v-f658ade8 {
  font-size: 24rpx;
  color: #ffffff;
  margin-bottom: 10rpx;
}
.vip-card .vip-info .vip-link.data-v-f658ade8 {
  margin-top: 15rpx;
  font-size: 30rpx;
  color: #ececec;
}
.package-list.data-v-f658ade8 {
  position: relative;
  z-index: 10;
  margin-top: 95rpx;
  padding: 0 24rpx;
}
.package-list .package-title.data-v-f658ade8 {
  font-size: 30rpx;
  color: #333;
  font-weight: bold;
  margin-bottom: 32rpx;
}
.package-list .package-grid.data-v-f658ade8 {
  display: flex;
  flex-wrap: wrap;
  gap: 32rpx;
}
.package-list .package-item.data-v-f658ade8 {
  width: calc((100% - 80rpx) / 3);
  background: #fff;
  border-radius: 18rpx;
  height: 160rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  border: 2rpx solid transparent;
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}
.package-list .package-item .package-content.data-v-f658ade8 {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 16rpx 8rpx;
}
.package-list .package-item .package-content .package-name.data-v-f658ade8 {
  font-size: 24rpx;
  color: #333;
  font-weight: 500;
  margin-bottom: 12rpx;
  text-align: center;
}
.package-list .package-item .package-content .package-price.data-v-f658ade8 {
  display: flex;
  align-items: baseline;
  justify-content: center;
  text-align: center;
}
.package-list .package-item .package-content .package-price .price-symbol.data-v-f658ade8 {
  font-size: 24rpx;
  color: #2c2c2c;
  font-weight: normal;
}
.package-list .package-item .package-content .package-price .price-amount.data-v-f658ade8 {
  font-size: 40rpx;
  color: #232323;
  font-weight: bold;
}
.package-list .package-item.active.data-v-f658ade8 {
  border: 3rpx solid #ffb300;
  background: #fffbe6;
  box-shadow: 0 4rpx 16rpx rgba(255, 179, 0, 0.2);
  transform: scale(1.02);
}
.package-list .package-item.active .package-content .package-name.data-v-f658ade8 {
  color: #ff9900;
}
.package-list .package-item.active .package-content .package-price .price-symbol.data-v-f658ade8 {
  color: #ff9900;
}
.package-list .package-item.active .package-content .package-price .price-amount.data-v-f658ade8 {
  color: #ff9900;
}
.package-list .buy-btn.data-v-f658ade8 {
  width: 100%;
  background: linear-gradient(90deg, #4BA1FC 0%, #7e6dff 100%);
  color: #fff;
  font-size: 32rpx;
  font-weight: bold;
  border-radius: 45rpx;
  border: none;
  margin: 40rpx 0 0 0;
}
.package-list .buy-btn.data-v-f658ade8:disabled {
  background: #d0d0d0;
  color: #fff;
}
.package-list .empty-state.data-v-f658ade8 {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 50rpx 0;
}
.buy-section.data-v-f658ade8 {
  display: none;
}