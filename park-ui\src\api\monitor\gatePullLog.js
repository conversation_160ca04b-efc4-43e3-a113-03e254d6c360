import request from '@/utils/request'

// 查询道闸推送日志列表
export function listGatePullLog(query) {
  return request({
    url: '/system/monitor/gatePullLog/list',
    method: 'get',
    params: query
  })
}

// 查询道闸推送日志详细
export function getGatePullLog(id) {
  return request({
    url: '/system/monitor/gatePullLog/' + id,
    method: 'get'
  })
}

// 导出道闸推送日志
export function exportGatePullLog(query) {
  return request({
    url: '/system/monitor/gatePullLog/export',
    method: 'post',
    params: query
  })
}

// 删除道闸推送日志
export function delGatePullLog(id) {
  return request({
    url: '/system/monitor/gatePullLog/' + id,
    method: 'delete'
  })
}

// 清空道闸推送日志
export function cleanGatePullLog() {
  return request({
    url: '/system/monitor/gatePullLog/clean',
    method: 'delete'
  })
}
