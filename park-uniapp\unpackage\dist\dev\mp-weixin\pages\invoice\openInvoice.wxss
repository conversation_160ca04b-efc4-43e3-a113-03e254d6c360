/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* uni.scss */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.open-invoice.data-v-b7ea1ec7 {
  min-height: 100vh;
  background: #f8f9fa;
}
.open-invoice .cell.data-v-b7ea1ec7 {
  padding: 0 32rpx;
}
.open-invoice .cell .cell-title.data-v-b7ea1ec7 {
  font-size: 32rpx;
  font-weight: 600;
  color: #000000;
  padding: 30rpx 0;
}
.open-invoice .cell .content.data-v-b7ea1ec7 {
  padding: 20rpx 32rpx;
  background: #ffffff;
  border-radius: 20rpx;
}
.open-invoice .cell .content .content_item.data-v-b7ea1ec7 {
  display: flex;
  align-items: center;
  position: relative;
  border-bottom: 1rpx solid rgba(189, 189, 189, 0.2);
  padding: 22rpx 0;
}
.open-invoice .cell .content .content_item.data-v-b7ea1ec7:last-child {
  border-bottom: none;
}
.open-invoice .cell .content .content_item .title.data-v-b7ea1ec7 {
  font-size: 28rpx;
  color: #212121;
  font-weight: 500;
  margin-right: 40rpx;
  min-width: 112rpx;
  word-break: keep-all;
}
.open-invoice .cell .content .content_item .word.data-v-b7ea1ec7 {
  flex: 1;
  font-size: 28rpx;
  color: #616161;
  line-height: 1.5;
}
.open-invoice .cell .content .content_item .word .money.data-v-b7ea1ec7 {
  font-weight: 600;
  color: #ff3333;
}
.open-invoice .cell .email.data-v-b7ea1ec7 {
  padding: 32rpx;
  background: #ffffff;
  border-radius: 20rpx;
}
.open-invoice .cell .email .content_item.data-v-b7ea1ec7 {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}
.open-invoice .cell .email .content_item .title.data-v-b7ea1ec7 {
  font-size: 28rpx;
  color: #212121;
  font-weight: 500;
  margin-right: 40rpx;
  min-width: 112rpx;
  word-break: keep-all;
}
.open-invoice .cell .email .content_item .email-input.data-v-b7ea1ec7 {
  flex: 1;
}
.open-invoice .cell .email .desc.data-v-b7ea1ec7 {
  font-size: 24rpx;
  color: #f5820e;
  margin-top: 35rpx;
}
.open-invoice .cell .btn.data-v-b7ea1ec7 {
  width: 100%;
  height: 70rpx;
  background: linear-gradient(90deg, #4BA1FC 0%, #7e6dff 100%);
  border-radius: 70rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 36rpx;
  font-weight: 600;
  color: #ffffff;
  margin: 30rpx 0;
}
.open-invoice .cell .btn.disabled.data-v-b7ea1ec7 {
  opacity: 0.6;
}
.open-invoice .cell .btn text.data-v-b7ea1ec7 {
  margin-left: 8rpx;
}
.open-invoice .tips_word.data-v-b7ea1ec7 {
  padding: 0 42rpx 60rpx;
}
.open-invoice .tips_word .tips_word_title.data-v-b7ea1ec7 {
  font-weight: 700;
  font-size: 36rpx;
  color: #2a304a;
  margin-bottom: 20rpx;
}
.open-invoice .tips_word .tips_word_desc.data-v-b7ea1ec7 {
  font-weight: 400;
  font-size: 28rpx;
  color: #a0a7c2;
  line-height: 1.4;
  margin-bottom: 16rpx;
}
.popup-cell.data-v-b7ea1ec7 {
  padding: 32rpx;
  background: #ebeef6;
  border-radius: 20rpx;
  max-height: 80vh;
}
.popup-cell .popup-title.data-v-b7ea1ec7 {
  font-weight: 600;
  font-size: 30rpx;
  color: #333333;
  border-left: 4rpx solid #246bfd;
  padding-left: 12rpx;
  margin-bottom: 32rpx;
}
.popup-cell .popup-content.data-v-b7ea1ec7 {
  max-height: 600rpx;
  overflow-y: scroll;
}
.popup-cell .popup-content .invoice-item.data-v-b7ea1ec7 {
  padding: 32rpx;
  background: #ffffff;
  border-radius: 20rpx;
  margin-bottom: 20rpx;
}
.popup-cell .popup-content .invoice-item .item-header.data-v-b7ea1ec7 {
  display: flex;
  align-items: center;
  padding-bottom: 20rpx;
  border-bottom: 2rpx solid rgba(189, 189, 189, 0.2);
}
.popup-cell .popup-content .invoice-item .item-header .header-text.data-v-b7ea1ec7 {
  font-size: 24rpx;
  font-weight: bold;
  color: #616161;
  margin-left: 8rpx;
}
.popup-cell .popup-content .invoice-item .item-title.data-v-b7ea1ec7 {
  font-size: 32rpx;
  font-weight: 500;
  color: #212121;
  margin: 20rpx 0 8rpx;
}
.popup-cell .popup-content .invoice-item .item-bottom.data-v-b7ea1ec7 {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.popup-cell .popup-content .invoice-item .item-bottom .item-desc.data-v-b7ea1ec7 {
  font-size: 24rpx;
  color: #9e9e9e;
  flex: 1;
}
.popup-cell .popup-content .invoice-item .item-bottom .item-edit.data-v-b7ea1ec7 {
  display: flex;
  align-items: center;
  font-size: 28rpx;
  color: #9e9e9e;
}
.popup-cell .popup-content .invoice-item .item-bottom .item-edit text.data-v-b7ea1ec7 {
  margin-left: 6rpx;
}
.popup-cell .add-btn.data-v-b7ea1ec7 {
  height: 88rpx;
  background: linear-gradient(90deg, #4ba1fc 0%, #7e6dff 100%);
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 30rpx;
  font-weight: 500;
  color: #ffffff;
  margin-top: 20rpx;
}
.popup-cell .add-btn text.data-v-b7ea1ec7 {
  margin-left: 8rpx;
}