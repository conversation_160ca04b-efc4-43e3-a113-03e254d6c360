2025-08-06 09:12:04.693  INFO 37676 --- [main] com.lgjy.wx.LgjyWxApplication            : The following 1 profile is active: "dev"
2025-08-06 09:12:07.547  INFO 37676 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Multiple Spring Data modules found, entering strict repository configuration mode
2025-08-06 09:12:07.554  INFO 37676 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-08-06 09:12:07.623  INFO 37676 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 24 ms. Found 0 Redis repository interfaces.
2025-08-06 09:12:08.285  INFO 37676 --- [main] o.s.cloud.context.scope.GenericScope     : BeanFactory id=164fa476-244e-3df1-8de0-03b9eafc3092
2025-08-06 09:12:09.965  INFO 37676 --- [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port(s): 9206 (http)
2025-08-06 09:12:09.999  INFO 37676 --- [main] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-08-06 09:12:10.000  INFO 37676 --- [main] org.apache.catalina.core.StandardEngine  : Starting Servlet engine: [Apache Tomcat/9.0.105]
2025-08-06 09:12:10.041  WARN 37676 --- [main] o.a.c.webresources.DirResourceSet        : Disabled the global canonical file name cache to protect against CVE-2024-56337 when starting the WebResourceSet at [C:\Users\<USER>\AppData\Local\Temp\tomcat-docbase.9206.4075960470067233756] which is part of the web application []
2025-08-06 09:12:10.417  INFO 37676 --- [main] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-08-06 09:12:10.418  INFO 37676 --- [main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 5650 ms
2025-08-06 09:12:10.637  INFO 37676 --- [main] c.a.d.s.b.a.DruidDataSourceAutoConfigure : Init DruidDataSource
2025-08-06 09:12:11.908  INFO 37676 --- [main] com.alibaba.druid.pool.DruidDataSource   : {dataSource-1} inited
2025-08-06 09:12:14.629  INFO 37676 --- [main] o.s.c.openfeign.FeignClientFactoryBean   : For 'park-gate' URL not provided. Will try picking an instance via load-balancing.
2025-08-06 09:12:15.276  INFO 37676 --- [main] c.a.c.sentinel.SentinelWebMvcConfigurer  : [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
2025-08-06 09:12:18.265  INFO 37676 --- [main] o.s.c.openfeign.FeignClientFactoryBean   : For 'park-system' URL not provided. Will try picking an instance via load-balancing.
2025-08-06 09:12:21.267  WARN 37676 --- [main] iguration$LoadBalancerCaffeineWarnLogger : Spring Cloud LoadBalancer is currently working with the default cache. While this cache implementation is useful for development and tests, it's recommended to use Caffeine cache in production.You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
2025-08-06 09:12:21.296  INFO 37676 --- [main] o.s.b.a.e.web.EndpointLinksResolver      : Exposing 1 endpoint(s) beneath base path '/actuator'
2025-08-06 09:12:21.523  INFO 37676 --- [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port(s): 9206 (http) with context path ''
2025-08-06 09:12:21.551  INFO 37676 --- [main] c.a.n.p.a.s.c.ClientAuthPluginManager    : [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-08-06 09:12:21.551  INFO 37676 --- [main] c.a.n.p.a.s.c.ClientAuthPluginManager    : [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-08-06 09:12:21.810  INFO 37676 --- [main] c.a.c.n.registry.NacosServiceRegistry    : nacos registry, DEFAULT_GROUP park-wx **************:9206 register finished
2025-08-06 09:12:22.877  INFO 37676 --- [main] com.lgjy.wx.LgjyWxApplication            : Started LgjyWxApplication in 24.878 seconds (JVM running for 27.455)
2025-08-06 09:12:22.920  INFO 37676 --- [main] c.a.c.n.refresh.NacosContextRefresher    : [Nacos Config] Listening config: dataId=park-wx.yml, group=DEFAULT_GROUP
2025-08-06 09:12:22.921  INFO 37676 --- [main] c.a.c.n.refresh.NacosContextRefresher    : [Nacos Config] Listening config: dataId=park-wx, group=DEFAULT_GROUP
2025-08-06 09:12:22.925  INFO 37676 --- [main] c.a.c.n.refresh.NacosContextRefresher    : [Nacos Config] Listening config: dataId=common-dev.yml, group=DEFAULT_GROUP
2025-08-06 09:12:22.928  INFO 37676 --- [main] c.a.c.n.refresh.NacosContextRefresher    : [Nacos Config] Listening config: dataId=park-wx-dev.yml, group=DEFAULT_GROUP
2025-08-06 09:12:22.940  INFO 37676 --- [scheduling-1] com.lgjy.wx.task.OrderTimeoutTask        : 开始执行订单超时处理任务
2025-08-06 09:12:23.208  INFO 37676 --- [scheduling-1] com.lgjy.wx.task.OrderTimeoutTask        : 订单超时处理任务完成，没有发现超时订单
2025-08-06 09:12:26.050  INFO 37676 --- [RMI TCP Connection(7)-**************] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-06 09:12:26.051  INFO 37676 --- [RMI TCP Connection(7)-**************] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-08-06 09:12:26.055  INFO 37676 --- [RMI TCP Connection(7)-**************] o.s.web.servlet.DispatcherServlet        : Completed initialization in 4 ms
2025-08-06 09:16:27.035  WARN 37676 --- [nacos.client.cachedata.internal.notifier] c.a.c.n.c.NacosPropertySourceBuilder     : Ignore the empty nacos configuration and get it based on dataId[park-wx] & group[DEFAULT_GROUP]
2025-08-06 09:16:27.040  WARN 37676 --- [nacos.client.cachedata.internal.notifier] c.a.c.n.c.NacosPropertySourceBuilder     : Ignore the empty nacos configuration and get it based on dataId[park-wx.yml] & group[DEFAULT_GROUP]
2025-08-06 09:16:27.044  INFO 37676 --- [nacos.client.cachedata.internal.notifier] b.c.PropertySourceBootstrapConfiguration : Located property source: [BootstrapPropertySource {name='bootstrapProperties-park-wx-dev.yml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-park-wx.yml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-park-wx,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-common-dev.yml,DEFAULT_GROUP'}]
2025-08-06 09:16:27.070  INFO 37676 --- [nacos.client.cachedata.internal.notifier] o.s.boot.SpringApplication               : The following 1 profile is active: "dev"
2025-08-06 09:16:27.096  INFO 37676 --- [nacos.client.cachedata.internal.notifier] o.s.boot.SpringApplication               : Started application in 0.654 seconds (JVM running for 271.674)
2025-08-06 09:16:27.660  INFO 37676 --- [nacos.client.cachedata.internal.notifier] o.s.c.e.event.RefreshEventListener       : Refresh keys changed: [union-pay-api.notifyUrlOrder, union-pay-api.notifyUrlVip, union-pay-api.notifyUrlOrderAlipay, union-pay-api.notifyUrlOrderInvoice]
