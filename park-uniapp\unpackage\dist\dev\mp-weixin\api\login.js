"use strict";
const utils_request = require("../utils/request.js");
const getCode = (loginForm) => utils_request.request.post("/wxauth/code", loginForm);
const login = (loginForm) => utils_request.request.post("/wxauth/login/code", loginForm);
const loginWx = (loginWxForm) => utils_request.request.post("/wxauth/login/wx", loginWxForm);
const getOpenid = (data) => utils_request.request.post("/wxauth/openid", data);
exports.getCode = getCode;
exports.getOpenid = getOpenid;
exports.login = login;
exports.loginWx = loginWx;
