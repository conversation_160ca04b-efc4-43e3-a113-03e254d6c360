package com.lgjy.system.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.lgjy.common.log.annotation.Log;
import com.lgjy.common.log.enums.BusinessType;
import com.lgjy.common.security.annotation.RequiresPermissions;
import com.lgjy.system.domain.GatePullLog;
import com.lgjy.system.service.IGatePullLogService;
import com.lgjy.common.core.utils.poi.ExcelUtil;
import com.lgjy.common.core.web.controller.BaseController;
import com.lgjy.common.core.web.domain.AjaxResult;
import com.lgjy.common.core.web.page.TableDataInfo;

/**
 * 道闸推送日志Controller
 * 
 * <AUTHOR>
 * @date 2025-08-03
 */
@RestController
@RequestMapping("/monitor/gatePullLog")
public class GatePullLogController extends BaseController
{
    @Autowired
    private IGatePullLogService gatePullLogService;

    /**
     * 查询道闸推送日志列表
     */
    @RequiresPermissions("monitor:gatePullLog:list")
    @GetMapping("/list")
    public TableDataInfo list(GatePullLog gatePullLog)
    {
        startPage();
        List<GatePullLog> list = gatePullLogService.selectGatePullLogList(gatePullLog);
        return getDataTable(list);
    }

    /**
     * 导出道闸推送日志列表
     */
    @RequiresPermissions("monitor:gatePullLog:export")
    @Log(title = "道闸推送日志", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, GatePullLog gatePullLog)
    {
        List<GatePullLog> list = gatePullLogService.selectGatePullLogList(gatePullLog);
        ExcelUtil<GatePullLog> util = new ExcelUtil<GatePullLog>(GatePullLog.class);
        util.exportExcel(response, list, "道闸推送日志数据");
    }

    /**
     * 获取道闸推送日志详细信息
     */
    @RequiresPermissions("monitor:gatePullLog:query")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") String id)
    {
        return success(gatePullLogService.selectGatePullLogById(id));
    }

    /**
     * 删除道闸推送日志
     */
    @RequiresPermissions("monitor:gatePullLog:remove")
    @Log(title = "道闸推送日志", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable String[] ids)
    {
        return toAjax(gatePullLogService.deleteGatePullLogByIds(ids));
    }

    /**
     * 清空道闸推送日志
     */
    @RequiresPermissions("monitor:gatePullLog:remove")
    @Log(title = "道闸推送日志", businessType = BusinessType.CLEAN)
    @DeleteMapping("/clean")
    public AjaxResult clean()
    {
        gatePullLogService.cleanGatePullLog();
        return success();
    }
}
