"use strict";
const utils_request = require("../utils/request.js");
const getPackageList = (data) => utils_request.request.post("/wx/package/list", data);
const getUserPackagePlateList = (data) => utils_request.request.post("/wx/package/user/plateList", data);
const getPurchaseCarList = (data) => utils_request.request.post("/wx/package/purchaseCarList", data);
const getRenewalCarList = (data) => utils_request.request.post("/wx/package/renewalCarList", data);
const getUserPackageRecordList = (data) => utils_request.request.post("/wx/package/user/record/list", data);
const createOrder = (data) => utils_request.request.post("/wx/package/order/create", data);
const updateOrder = (data) => utils_request.request.post("/wx/package/order/update", data);
const checkCarInWarehouse = (data) => utils_request.request.post("/wx/package/packageJudge", data);
const getVipUserPackageList = (vipType) => utils_request.request.get(`/wx/package/vip/list/${vipType}`);
exports.checkCarInWarehouse = checkCarInWarehouse;
exports.createOrder = createOrder;
exports.getPackageList = getPackageList;
exports.getPurchaseCarList = getPurchaseCarList;
exports.getRenewalCarList = getRenewalCarList;
exports.getUserPackagePlateList = getUserPackagePlateList;
exports.getUserPackageRecordList = getUserPackageRecordList;
exports.getVipUserPackageList = getVipUserPackageList;
exports.updateOrder = updateOrder;
