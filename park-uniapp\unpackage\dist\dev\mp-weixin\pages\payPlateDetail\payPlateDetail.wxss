/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* uni.scss */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.submit-btn.data-v-a7de0af5 {
  margin-top: 40rpx;
  width: 100%;
  background: linear-gradient(90deg, #4BA1FC 0%, #9b8eff 100%);
  border-radius: 44rpx;
  color: #fff;
  font-size: 32rpx;
  border: none;
}
.submit-btn[disabled].data-v-a7de0af5 {
  background: #cccccc;
  color: #999999;
}
.payment-notice.data-v-a7de0af5 {
  margin-top: 20rpx;
  text-align: center;
  font-size: 24rpx;
  color: #999999;
  line-height: 1.4;
}
.pay-plate-detail.data-v-a7de0af5 {
  height: 100vh;
  background-color: #f5f5f5;
}
.pay-plate-detail .cell.data-v-a7de0af5 {
  padding: 40rpx 32rpx 0;
}
.pay-plate-detail .cell .top-cell.data-v-a7de0af5 {
  margin-bottom: 20rpx;
  display: flex;
  align-items: center;
}
.pay-plate-detail .cell .top-cell .top-cell-title.data-v-a7de0af5 {
  margin-right: 8rpx;
}
.pay-plate-detail .cell .top-cell .top-cell-title .title.data-v-a7de0af5 {
  font-size: 40rpx;
  font-weight: bold;
  color: #212121;
  margin-bottom: 8rpx;
}
.pay-plate-detail .cell .top-cell .top-cell-title .desc.data-v-a7de0af5 {
  font-size: 28rpx;
  font-weight: 400;
  color: #9e9e9e;
}
.pay-plate-detail .cell .top-cell image.data-v-a7de0af5 {
  width: 284rpx;
  height: 200rpx;
}
.pay-plate-detail .cell .form_cell.data-v-a7de0af5 {
  padding: 32rpx;
  border-radius: 20rpx;
  background-color: #fff;
}
.pay-plate-detail .cell .form_cell .form_cell_top.data-v-a7de0af5 {
  margin-bottom: 32rpx;
}
.pay-plate-detail .cell .form_cell .form_cell_top .form_cell_top_plateNo.data-v-a7de0af5 {
  display: flex;
  align-items: center;
  font-size: 36rpx;
  font-weight: bold;
  color: #212121;
}
.pay-plate-detail .cell .form_cell .form_cell_top .form_cell_top_plateNo image.data-v-a7de0af5 {
  width: 48rpx;
  height: 48rpx;
  margin-right: 12rpx;
}
.pay-plate-detail .cell .form_cell .form_cell_top .form_cell_top_edit.data-v-a7de0af5 {
  padding: 4rpx 12rpx;
  background: #e9f0ff;
  border-radius: 4rpx;
  font-size: 20rpx;
  font-weight: 400;
  color: #246bfd;
}
.pay-plate-detail .cell .form_cell .form_cell_top .form_cell_top_edit image.data-v-a7de0af5 {
  width: 32rpx;
  height: 32rpx;
  margin-right: 8rpx;
}
.pay-plate-detail .cell .form_cell .form_cell_word.data-v-a7de0af5 {
  display: flex;
  align-items: center;
  font-size: 28rpx;
  font-weight: 400;
  color: #9e9e9e;
  margin-bottom: 32rpx;
}
.pay-plate-detail .cell .form_cell .form_cell_word image.data-v-a7de0af5 {
  width: 36rpx;
  height: 36rpx;
  margin-right: 12rpx;
}
.pay-plate-detail .cell .form_cell .form_cell_word .tips.data-v-a7de0af5 {
  font-size: 28rpx;
  font-weight: 400;
  color: #616161;
}
.pay-plate-detail .cell .form_cell .dikou .unit.data-v-a7de0af5 {
  font-size: 24rpx;
  margin-right: 8rpx;
  color: #ff922e;
  padding-top: 8rpx;
}
.pay-plate-detail .cell .form_cell .dikou .desc.data-v-a7de0af5 {
  font-size: 28rpx;
  margin-right: 20rpx;
  font-weight: 400;
  color: #ff922e;
}
.pay-plate-detail .cell .form_cell .dikou .price.data-v-a7de0af5 {
  color: #ff922e;
  font-size: 36rpx;
  font-weight: Bold;
}
.pay-plate-detail .cell .form_cell .dikou image.data-v-a7de0af5 {
  width: 28rpx;
  height: 28rpx;
  margin-left: 10rpx;
}
.pay-plate-detail .cell .form_cell .form_cell_bottom.data-v-a7de0af5 {
  border-top: 2rpx solid rgba(189, 189, 189, 0.2);
  padding-top: 16rpx;
  text-align: right;
}
.pay-plate-detail .cell .form_cell .form_cell_bottom .word.data-v-a7de0af5 {
  font-size: 28rpx;
  font-weight: bold;
  color: #212121;
}
.pay-plate-detail .cell .form_cell .form_cell_bottom .number.data-v-a7de0af5 {
  font-size: 48rpx;
  font-weight: bold;
  color: #f90355;
}
.pay-plate-detail .cell .form_cell .form_cell_bottom .number .tips.data-v-a7de0af5 {
  font-size: 28rpx;
  font-weight: bold;
  color: #f90355;
}